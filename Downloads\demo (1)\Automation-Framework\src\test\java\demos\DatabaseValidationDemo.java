package demos;

import database.DatabaseManager;
import database.DatabaseValidator;
import drivers.DriverManager;
import models.DatabaseValidationResult;
import utils.DatabaseValidationExecutor;
import utils.TestCaseExecutor;

import java.util.List;

/**
 * Demo class showing how to use the Database Validation Framework
 */
public class DatabaseValidationDemo {

    public static void main(String[] args) {
        System.out.println("🚀 Database Validation Framework Demo");
        System.out.println("=" .repeat(60));

        try {
            // Demo 1: Test database connection
            System.out.println("\n📋 Demo 1: Testing Database Connection");
            testDatabaseConnection();

            // Demo 2: Validate configuration
            System.out.println("\n📋 Demo 2: Validating Configuration");
            validateConfiguration();

            // Demo 3: Show available test cases with database validation
            System.out.println("\n📋 Demo 3: Finding Test Cases with Database Validation");
            showTestCasesWithDatabaseValidation();

            // Demo 4: Execute database validation for a test case
            System.out.println("\n📋 Demo 4: Executing Database Validation");
            executeDatabaseValidation();

            // Demo 5: Complete flow - UI + Database validation
            System.out.println("\n📋 Demo 5: Complete UI + Database Validation Flow");
            executeCompleteValidationFlow();

        } catch (Exception e) {
            System.err.println("❌ Demo execution error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Cleanup
            cleanup();
        }
    }

    /**
     * Demo 1: Test database connection
     */
    private static void testDatabaseConnection() {
        System.out.println("🔍 Testing database connectivity...");
        
        try {
            DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
            boolean isValid = dbValidator.validateConfiguration();
            
            if (isValid) {
                System.out.println("✅ Database connection successful!");
                
                // Show database info
                List<String> tables = DatabaseValidator.getTableList();
                if (!tables.isEmpty()) {
                    System.out.println("📋 Available tables (first 10):");
                    tables.stream().limit(10).forEach(table -> 
                        System.out.println("   - " + table));
                    
                    if (tables.size() > 10) {
                        System.out.println("   ... and " + (tables.size() - 10) + " more tables");
                    }
                }
            } else {
                System.out.println("❌ Database connection failed!");
                System.out.println("💡 Please check your config.properties database settings");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Database connection error: " + e.getMessage());
            System.out.println("💡 Make sure to configure database settings in config.properties");
        }
    }

    /**
     * Demo 2: Validate configuration
     */
    private static void validateConfiguration() {
        System.out.println("🔍 Validating database validation configuration...");
        
        DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
        boolean isValid = dbValidator.validateConfiguration();
        
        if (!isValid) {
            System.out.println("\n📋 Configuration Help:");
            dbValidator.printConfigurationHelp();
        }
    }

    /**
     * Demo 3: Show test cases with database validation
     */
    private static void showTestCasesWithDatabaseValidation() {
        System.out.println("🔍 Finding test cases with database validation configured...");
        
        try {
            DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
            List<String> testCases = dbValidator.getTestCasesWithDatabaseValidation();
            
            if (testCases.isEmpty()) {
                System.out.println("⚠️ No test cases found with database validation configured");
                System.out.println("💡 Add database validation columns to your Excel file:");
                System.out.println("   Column N: DB_TABLE");
                System.out.println("   Column O: DB_COLUMNS");
                System.out.println("   Column P: DB_WHERE_CONDITION");
                System.out.println("   Column Q: DB_EXPECTED_VALUES");
            } else {
                System.out.println("✅ Found " + testCases.size() + " test cases with database validation:");
                testCases.forEach(testCase -> 
                    System.out.println("   - " + testCase));
            }
            
        } catch (Exception e) {
            System.out.println("❌ Error finding test cases: " + e.getMessage());
        }
    }

    /**
     * Demo 4: Execute database validation
     */
    private static void executeDatabaseValidation() {
        System.out.println("🔍 Executing database validation for test case...");
        
        try {
            DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
            
            // Get test cases with database validation
            List<String> testCases = dbValidator.getTestCasesWithDatabaseValidation();
            
            if (testCases.isEmpty()) {
                System.out.println("⚠️ No test cases with database validation found");
                System.out.println("💡 Configure database validation in your Excel file first");
                return;
            }
            
            // Use the first test case found
            String testCaseId = testCases.get(0);
            System.out.println("🎯 Validating test case: " + testCaseId);
            
            // Execute database validation
            DatabaseValidationResult result = dbValidator.executeValidation(testCaseId);
            
            // Show results
            System.out.println("\n📊 Validation Results:");
            System.out.println(result.getSummary());
            
            if (!result.isOverallResult()) {
                System.out.println("\n📋 Detailed Report:");
                System.out.println(result.getDetailedReport());
            }
            
        } catch (Exception e) {
            System.out.println("❌ Database validation error: " + e.getMessage());
        }
    }

    /**
     * Demo 5: Complete UI + Database validation flow
     */
    private static void executeCompleteValidationFlow() {
        System.out.println("🔍 Executing complete UI + Database validation flow...");
        
        try {
            // Initialize WebDriver for UI testing
            DriverManager.initDriver();
            
            // Execute UI test case
            System.out.println("🎭 Step 1: Executing UI test case...");
            TestCaseExecutor testExecutor = new TestCaseExecutor();
            
            // Get available test cases
            List<String> availableTestCases = testExecutor.getAvailableTestCases();
            if (availableTestCases.isEmpty()) {
                System.out.println("⚠️ No test cases found in Excel");
                return;
            }
            
            String testCaseId = availableTestCases.get(0);
            System.out.println("🎯 Executing UI test case: " + testCaseId);
            
            // Execute the UI test
            testExecutor.executeTestCase(testCaseId);
            
            // Execute database validation
            System.out.println("\n🔍 Step 2: Executing database validation...");
            DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
            DatabaseValidationResult dbResult = dbValidator.executeValidation(testCaseId);
            
            // Combined results
            System.out.println("\n🎯 Combined Validation Results:");
            System.out.println("   UI Test: ✅ Completed");
            System.out.println("   Database Validation: " + (dbResult.isOverallResult() ? "✅ PASSED" : "❌ FAILED"));
            
            if (dbResult.hasDatabaseValidations()) {
                System.out.println("   Database Steps: " + dbResult.getPassedSteps() + "/" + 
                                 (dbResult.getPassedSteps() + dbResult.getFailedSteps()) + " passed");
            } else {
                System.out.println("   Database Steps: ⏭️ No database validation configured");
            }
            
            // Show database validation summary
            if (dbResult.hasDatabaseValidations()) {
                System.out.println("\n📊 Database Validation Summary:");
                System.out.println(dbResult.getSummary());
            }
            
        } catch (Exception e) {
            System.out.println("❌ Complete validation flow error: " + e.getMessage());
        }
    }

    /**
     * Cleanup resources
     */
    private static void cleanup() {
        System.out.println("\n🧹 Cleaning up resources...");
        
        try {
            // Close WebDriver
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
            
            // Close database connections
            DatabaseManager.shutdown();
            
            System.out.println("✅ Cleanup completed");
            
        } catch (Exception e) {
            System.out.println("⚠️ Cleanup warning: " + e.getMessage());
        }
        
        System.out.println("\n🎉 Database Validation Demo completed!");
        System.out.println("=" .repeat(60));
    }

    /**
     * Helper method to demonstrate database validation setup
     */
    public static void printSetupInstructions() {
        System.out.println("📋 Database Validation Setup Instructions:");
        System.out.println("=" .repeat(60));
        System.out.println();
        System.out.println("1. 🔧 Configure Database Connection:");
        System.out.println("   Edit config.properties and set:");
        System.out.println("   db.type=mysql");
        System.out.println("   db.host=localhost");
        System.out.println("   db.port=3306");
        System.out.println("   db.name=your_database_name");
        System.out.println("   db.username=your_username");
        System.out.println("   db.password=your_password");
        System.out.println();
        System.out.println("2. 📊 Add Database Validation Columns to Excel:");
        System.out.println("   Column N: DB_TABLE (e.g., 'users')");
        System.out.println("   Column O: DB_COLUMNS (e.g., 'email,status')");
        System.out.println("   Column P: DB_WHERE_CONDITION (e.g., 'email={{TEST_DATA}}')");
        System.out.println("   Column Q: DB_EXPECTED_VALUES (e.g., '<EMAIL>,active')");
        System.out.println();
        System.out.println("3. 🎯 Example for Sign-up Validation:");
        System.out.println("   TC001 | Enter Email | <EMAIL> | xpath | users | email,status | email='{{TEST_DATA}}' | <EMAIL>,active");
        System.out.println();
        System.out.println("4. ▶️ Run the validation:");
        System.out.println("   DatabaseValidationExecutor executor = new DatabaseValidationExecutor();");
        System.out.println("   DatabaseValidationResult result = executor.executeValidation(\"TC001\");");
        System.out.println();
        System.out.println("=" .repeat(60));
    }
}
