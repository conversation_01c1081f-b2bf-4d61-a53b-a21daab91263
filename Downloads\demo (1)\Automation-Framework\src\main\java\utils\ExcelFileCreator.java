package utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class to create sample Excel files for testing
 */
public class ExcelFileCreator {

    public static void main(String[] args) {
        try {
            createLLCExcelFile();
            createCorpExcelFile();
            System.out.println("✅ Sample Excel files created successfully!");
        } catch (Exception e) {
            System.err.println("❌ Error creating Excel files: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Creates LLC.xlsx with sample data
     */
    public static void createLLCExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        
        // Create "Entity Formation" sheet
        Sheet entityFormationSheet = workbook.createSheet("Entity Formation");
        createEntityFormationData(entityFormationSheet, "LLC");
        
        // Create "Compliance" sheet
        Sheet complianceSheet = workbook.createSheet("Compliance");
        createComplianceData(complianceSheet, "LLC");
        
        // Write to file
        String filePath = "src/resources/testdata/LLC.xlsx";
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
        }
        
        workbook.close();
        System.out.println("✅ Created: " + filePath);
    }

    /**
     * Creates Corp.xlsx with sample data
     */
    public static void createCorpExcelFile() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        
        // Create "Entity Formation" sheet
        Sheet entityFormationSheet = workbook.createSheet("Entity Formation");
        createEntityFormationData(entityFormationSheet, "Corp");
        
        // Create "Compliance" sheet
        Sheet complianceSheet = workbook.createSheet("Compliance");
        createComplianceData(complianceSheet, "Corp");
        
        // Write to file
        String filePath = "src/resources/testdata/Corp.xlsx";
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
        }
        
        workbook.close();
        System.out.println("✅ Created: " + filePath);
    }

    /**
     * Creates Entity Formation sheet data
     */
    private static void createEntityFormationData(Sheet sheet, String entityType) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        List<String> headers = Arrays.asList("Product Name", "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia");
        
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            
            // Style header
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        String[][] sampleData;
        if (entityType.equals("LLC")) {
            sampleData = new String[][] {
                {"Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation", "", "Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation", "Basic LLC Formation"},
                {"Expedited LLC Formation", "Expedited LLC Formation", "", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation", "Expedited LLC Formation"},
                {"Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service"},
                {"EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service"},
                {"Operating Agreement", "Operating Agreement", "Operating Agreement", "", "Operating Agreement", "Operating Agreement", "Operating Agreement", "Operating Agreement", "Operating Agreement", "Operating Agreement", "Operating Agreement"}
            };
        } else {
            sampleData = new String[][] {
                {"Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation", "", "Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation", "Basic Corporation Formation"},
                {"Expedited Corporation Formation", "Expedited Corporation Formation", "", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation", "Expedited Corporation Formation"},
                {"Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service", "Registered Agent Service"},
                {"EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service", "EIN Service"},
                {"Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws", "", "Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws", "Corporate Bylaws"}
            };
        }
        
        for (int rowIndex = 0; rowIndex < sampleData.length; rowIndex++) {
            Row row = sheet.createRow(rowIndex + 1);
            for (int colIndex = 0; colIndex < sampleData[rowIndex].length; colIndex++) {
                Cell cell = row.createCell(colIndex);
                cell.setCellValue(sampleData[rowIndex][colIndex]);
            }
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.size(); i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Creates Compliance sheet data
     */
    private static void createComplianceData(Sheet sheet, String entityType) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        List<String> headers = Arrays.asList("Product Name", "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia");
        
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            
            // Style header
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            cell.setCellStyle(headerStyle);
        }
        
        // Create data rows
        String[][] sampleData = {
            {"Annual Report Filing", "Annual Report Filing", "Annual Report Filing", "", "Annual Report Filing", "Annual Report Filing", "Annual Report Filing", "Annual Report Filing", "Annual Report Filing", "Annual Report Filing", "Annual Report Filing"},
            {"State Tax Registration", "State Tax Registration", "", "State Tax Registration", "State Tax Registration", "State Tax Registration", "State Tax Registration", "State Tax Registration", "State Tax Registration", "State Tax Registration", "State Tax Registration"},
            {"Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research", "Business License Research"},
            {"Compliance Calendar", "Compliance Calendar", "Compliance Calendar", "", "Compliance Calendar", "Compliance Calendar", "Compliance Calendar", "Compliance Calendar", "Compliance Calendar", "Compliance Calendar", "Compliance Calendar"}
        };
        
        for (int rowIndex = 0; rowIndex < sampleData.length; rowIndex++) {
            Row row = sheet.createRow(rowIndex + 1);
            for (int colIndex = 0; colIndex < sampleData[rowIndex].length; colIndex++) {
                Cell cell = row.createCell(colIndex);
                cell.setCellValue(sampleData[rowIndex][colIndex]);
            }
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.size(); i++) {
            sheet.autoSizeColumn(i);
        }
    }
}
