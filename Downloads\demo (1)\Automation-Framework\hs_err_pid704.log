#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 697136 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=704, tid=17596
#
# JRE version: Java(TM) SE Runtime Environment (17.0.11+7) (build 17.0.11+7-LTS-207)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -ea -DBROWSER=CHROME -Didea.test.cyclic.buffer.size=1048576 -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\lib\idea_rt.jar=64777:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\bin -Dfile.encoding=UTF-8 com.intellij.rt.testng.RemoteTestNGStarter -usedefaultlisteners false -socket64776 @w@C:\Users\<USER>\AppData\Local\Temp\idea_working_dirs_testng.tmp -temp C:\Users\<USER>\AppData\Local\Temp\idea_testng.tmp

Host: AMD Ryzen 3 3250U with Radeon Graphics         , 4 cores, 5G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Fri Jun  6 15:29:30 2025 India Standard Time elapsed time: 16.554036 seconds (0d 0h 0m 16s)

---------------  T H R E A D  ---------------

Current thread (0x00000200af4f8880):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=17596, stack(0x0000004910600000,0x0000004910700000)]


Current CompileTask:
C2:  16554 3555       4       jdk.internal.jimage.ImageLocation::verify (218 bytes)

Stack: [0x0000004910600000,0x0000004910700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x679cca]
V  [jvm.dll+0x7da13d]
V  [jvm.dll+0x7dba83]
V  [jvm.dll+0x7dc0f3]
V  [jvm.dll+0x2449af]
V  [jvm.dll+0xab83b]
V  [jvm.dll+0xabe1c]
V  [jvm.dll+0x2a9e6f]
V  [jvm.dll+0x5818d9]
V  [jvm.dll+0x21ed62]
V  [jvm.dll+0x21f13f]
V  [jvm.dll+0x218331]
V  [jvm.dll+0x215505]
V  [jvm.dll+0x1a2040]
V  [jvm.dll+0x225a2b]
V  [jvm.dll+0x223bcb]
V  [jvm.dll+0x7903ec]
V  [jvm.dll+0x78a85a]
V  [jvm.dll+0x678bb5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000200f7870df0, length=25, elements={
0x0000020092751730, 0x00000200af4debf0, 0x00000200af4e0230, 0x00000200af4f4150,
0x00000200af4f6a30, 0x00000200af4f7300, 0x00000200af4f7bd0, 0x00000200af4f8880,
0x00000200af4fd350, 0x00000200af56d330, 0x00000200af6d03d0, 0x00000200af8a2cf0,
0x00000200af8acc90, 0x00000200f73b9010, 0x00000200f6e81840, 0x00000200f5862580,
0x00000200f5865310, 0x00000200f5862a90, 0x00000200f5862070, 0x00000200f5863ed0,
0x00000200f5865820, 0x00000200f5862fa0, 0x00000200f58634b0, 0x00000200f58648f0,
0x00000200f5ce7ff0
}

Java Threads: ( => current thread )
  0x0000020092751730 JavaThread "main" [_thread_in_Java, id=25024, stack(0x000000490f900000,0x000000490fa00000)]
  0x00000200af4debf0 JavaThread "Reference Handler" daemon [_thread_blocked, id=8640, stack(0x0000004910000000,0x0000004910100000)]
  0x00000200af4e0230 JavaThread "Finalizer" daemon [_thread_blocked, id=8512, stack(0x0000004910100000,0x0000004910200000)]
  0x00000200af4f4150 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15528, stack(0x0000004910200000,0x0000004910300000)]
  0x00000200af4f6a30 JavaThread "Attach Listener" daemon [_thread_blocked, id=25136, stack(0x0000004910300000,0x0000004910400000)]
  0x00000200af4f7300 JavaThread "Service Thread" daemon [_thread_blocked, id=14240, stack(0x0000004910400000,0x0000004910500000)]
  0x00000200af4f7bd0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=25488, stack(0x0000004910500000,0x0000004910600000)]
=>0x00000200af4f8880 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=17596, stack(0x0000004910600000,0x0000004910700000)]
  0x00000200af4fd350 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=17012, stack(0x0000004910700000,0x0000004910800000)]
  0x00000200af56d330 JavaThread "Sweeper thread" daemon [_thread_blocked, id=21664, stack(0x0000004910800000,0x0000004910900000)]
  0x00000200af6d03d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=20776, stack(0x0000004910900000,0x0000004910a00000)]
  0x00000200af8a2cf0 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=10904, stack(0x0000004910a00000,0x0000004910b00000)]
  0x00000200af8acc90 JavaThread "Notification Thread" daemon [_thread_blocked, id=21084, stack(0x0000004910b00000,0x0000004910c00000)]
  0x00000200f73b9010 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=17176, stack(0x0000004910f00000,0x0000004911000000)]
  0x00000200f6e81840 JavaThread "AsyncHttpClient-1-1" daemon [_thread_blocked, id=25936, stack(0x0000004911100000,0x0000004911200000)]
  0x00000200f5862580 JavaThread "Exec Default Executor" daemon [_thread_in_native, id=24896, stack(0x0000004911200000,0x0000004911300000)]
  0x00000200f5865310 JavaThread "Exec Stream Pumper" daemon [_thread_in_native, id=12976, stack(0x0000004911300000,0x0000004911400000)]
  0x00000200f5862a90 JavaThread "Exec Stream Pumper" daemon [_thread_in_native, id=9688, stack(0x0000004911400000,0x0000004911500000)]
  0x00000200f5862070 JavaThread "Driver Service Executor" daemon [_thread_blocked, id=10068, stack(0x0000004911500000,0x0000004911600000)]
  0x00000200f5863ed0 JavaThread "Driver Service Executor" daemon [_thread_blocked, id=456, stack(0x0000004911600000,0x0000004911700000)]
  0x00000200f5865820 JavaThread "UrlChecker-2" daemon [_thread_blocked, id=9836, stack(0x0000004911700000,0x0000004911800000)]
  0x00000200f5862fa0 JavaThread "AsyncHttpClient-1-2" daemon [_thread_in_native, id=15104, stack(0x0000004911a00000,0x0000004911b00000)]
  0x00000200f58634b0 JavaThread "AsyncHttpClient-1-3" daemon [_thread_in_native, id=21756, stack(0x0000004911b00000,0x0000004911c00000)]
  0x00000200f58648f0 JavaThread "AsyncHttpClient-1-4" daemon [_thread_in_native, id=14100, stack(0x0000004911c00000,0x0000004911d00000)]
  0x00000200f5ce7ff0 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=25628, stack(0x0000004911900000,0x0000004911a00000)]

Other Threads:
  0x00000200af4da6d0 VMThread "VM Thread" [stack: 0x000000490ff00000,0x0000004910000000] [id=13376]
  0x00000200af8b21a0 WatcherThread [stack: 0x0000004910c00000,0x0000004910d00000] [id=10180]
  0x000002009279cba0 GCTaskThread "GC Thread#0" [stack: 0x000000490fa00000,0x000000490fb00000] [id=10920]
  0x00000200af9645c0 GCTaskThread "GC Thread#1" [stack: 0x0000004910d00000,0x0000004910e00000] [id=14360]
  0x00000200afa89c10 GCTaskThread "GC Thread#2" [stack: 0x0000004910e00000,0x0000004910f00000] [id=13408]
  0x00000200f5543a20 GCTaskThread "GC Thread#3" [stack: 0x0000004911000000,0x0000004911100000] [id=4816]
  0x00000200927a9840 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000490fb00000,0x000000490fc00000] [id=24284]
  0x00000200927abe40 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000490fc00000,0x000000490fd00000] [id=21624]
  0x00000200af3940b0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000490fd00000,0x000000490fe00000] [id=14212]
  0x00000200af3949f0 ConcurrentGCThread "G1 Service" [stack: 0x000000490fe00000,0x000000490ff00000] [id=19824]

Threads with active compile tasks:
C2 CompilerThread0    16623 3555       4       jdk.internal.jimage.ImageLocation::verify (218 bytes)
C1 CompilerThread0    16623 3577       3       org.apache.xmlbeans.impl.schema.XsbReader::readXmlValueObject (340 bytes)
C2 CompilerThread1    16623 3559       4       jdk.internal.module.SystemModuleFinders$SystemModuleReader::find (59 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0e00000, size: 1522 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000200b0000000-0x00000200b0bd0000-0x00000200b0bd0000), size 12386304, SharedBaseAddress: 0x00000200b0000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000200b1000000-0x00000200f1000000, reserved size: 1073741824
Narrow klass base: 0x00000200b0000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 6081M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 96M
 Heap Max Capacity: 1522M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 75776K, used 48083K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 2 survivors (2048K)
 Metaspace       used 35420K, committed 35776K, reserved 1114112K
  class space    used 4390K, committed 4544K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%|HS|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Complete 
|   1|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Untracked 
|   2|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Untracked 
|   3|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Untracked 
|   4|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Untracked 
|   5|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Untracked 
|   6|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Untracked 
|   7|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Untracked 
|   8|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Untracked 
|   9|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Untracked 
|  10|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Untracked 
|  11|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Untracked 
|  12|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Untracked 
|  13|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1be0200, 0x00000000a1b00000| Untracked 
|  14|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1c00000, 0x00000000a1c00000| Untracked 
|  15|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1d00000, 0x00000000a1d00000| Untracked 
|  16|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1e00000, 0x00000000a1e00000| Untracked 
|  17|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a1f00000, 0x00000000a1f00000| Untracked 
|  18|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2000000, 0x00000000a2000000| Untracked 
|  19|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%|HS|  |TAMS 0x00000000a2100000, 0x00000000a2100000| Complete 
|  20|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%|HC|  |TAMS 0x00000000a2200000, 0x00000000a2200000| Complete 
|  21|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%|HC|  |TAMS 0x00000000a2300000, 0x00000000a2300000| Complete 
|  22|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%|HC|  |TAMS 0x00000000a2400000, 0x00000000a2400000| Complete 
|  23|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%|HC|  |TAMS 0x00000000a2500000, 0x00000000a2500000| Complete 
|  24|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2600000| Untracked 
|  25|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2700000| Untracked 
|  26|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2800000| Untracked 
|  27|0x00000000a2900000, 0x00000000a29f4c00, 0x00000000a2a00000| 95%| O|  |TAMS 0x00000000a2900000, 0x00000000a2900000| Untracked 
|  28|0x00000000a2a00000, 0x00000000a2a00000, 0x00000000a2b00000|  0%| F|  |TAMS 0x00000000a2a00000, 0x00000000a2a00000| Untracked 
|  29|0x00000000a2b00000, 0x00000000a2b00000, 0x00000000a2c00000|  0%| F|  |TAMS 0x00000000a2b00000, 0x00000000a2b00000| Untracked 
|  30|0x00000000a2c00000, 0x00000000a2c00000, 0x00000000a2d00000|  0%| F|  |TAMS 0x00000000a2c00000, 0x00000000a2c00000| Untracked 
|  31|0x00000000a2d00000, 0x00000000a2d00000, 0x00000000a2e00000|  0%| F|  |TAMS 0x00000000a2d00000, 0x00000000a2d00000| Untracked 
|  32|0x00000000a2e00000, 0x00000000a2e00000, 0x00000000a2f00000|  0%| F|  |TAMS 0x00000000a2e00000, 0x00000000a2e00000| Untracked 
|  33|0x00000000a2f00000, 0x00000000a2f00000, 0x00000000a3000000|  0%| F|  |TAMS 0x00000000a2f00000, 0x00000000a2f00000| Untracked 
|  34|0x00000000a3000000, 0x00000000a3000000, 0x00000000a3100000|  0%| F|  |TAMS 0x00000000a3000000, 0x00000000a3000000| Untracked 
|  35|0x00000000a3100000, 0x00000000a3100000, 0x00000000a3200000|  0%| F|  |TAMS 0x00000000a3100000, 0x00000000a3100000| Untracked 
|  36|0x00000000a3200000, 0x00000000a3200000, 0x00000000a3300000|  0%| F|  |TAMS 0x00000000a3200000, 0x00000000a3200000| Untracked 
|  37|0x00000000a3300000, 0x00000000a3300000, 0x00000000a3400000|  0%| F|  |TAMS 0x00000000a3300000, 0x00000000a3300000| Untracked 
|  38|0x00000000a3400000, 0x00000000a3400000, 0x00000000a3500000|  0%| F|  |TAMS 0x00000000a3400000, 0x00000000a3400000| Untracked 
|  39|0x00000000a3500000, 0x00000000a3500000, 0x00000000a3600000|  0%| F|  |TAMS 0x00000000a3500000, 0x00000000a3500000| Untracked 
|  40|0x00000000a3600000, 0x00000000a3600000, 0x00000000a3700000|  0%| F|  |TAMS 0x00000000a3600000, 0x00000000a3600000| Untracked 
|  41|0x00000000a3700000, 0x00000000a3700000, 0x00000000a3800000|  0%| F|  |TAMS 0x00000000a3700000, 0x00000000a3700000| Untracked 
|  42|0x00000000a3800000, 0x00000000a3800000, 0x00000000a3900000|  0%| F|  |TAMS 0x00000000a3800000, 0x00000000a3800000| Untracked 
|  43|0x00000000a3900000, 0x00000000a3900000, 0x00000000a3a00000|  0%| F|  |TAMS 0x00000000a3900000, 0x00000000a3900000| Untracked 
|  44|0x00000000a3a00000, 0x00000000a3a00000, 0x00000000a3b00000|  0%| F|  |TAMS 0x00000000a3a00000, 0x00000000a3a00000| Untracked 
|  45|0x00000000a3b00000, 0x00000000a3b00000, 0x00000000a3c00000|  0%| F|  |TAMS 0x00000000a3b00000, 0x00000000a3b00000| Untracked 
|  46|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| S|CS|TAMS 0x00000000a3c00000, 0x00000000a3c00000| Complete 
|  47|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| S|CS|TAMS 0x00000000a3d00000, 0x00000000a3d00000| Complete 
|  48|0x00000000a3e00000, 0x00000000a3e00000, 0x00000000a3f00000|  0%| F|  |TAMS 0x00000000a3e00000, 0x00000000a3e00000| Untracked 
|  49|0x00000000a3f00000, 0x00000000a3f00000, 0x00000000a4000000|  0%| F|  |TAMS 0x00000000a3f00000, 0x00000000a3f00000| Untracked 
|  50|0x00000000a4000000, 0x00000000a4000000, 0x00000000a4100000|  0%| F|  |TAMS 0x00000000a4000000, 0x00000000a4000000| Untracked 
|  51|0x00000000a4100000, 0x00000000a4100000, 0x00000000a4200000|  0%| F|  |TAMS 0x00000000a4100000, 0x00000000a4100000| Untracked 
|  52|0x00000000a4200000, 0x00000000a4200000, 0x00000000a4300000|  0%| F|  |TAMS 0x00000000a4200000, 0x00000000a4200000| Untracked 
|  53|0x00000000a4300000, 0x00000000a4300000, 0x00000000a4400000|  0%| F|  |TAMS 0x00000000a4300000, 0x00000000a4300000| Untracked 
|  54|0x00000000a4400000, 0x00000000a4400000, 0x00000000a4500000|  0%| F|  |TAMS 0x00000000a4400000, 0x00000000a4400000| Untracked 
|  55|0x00000000a4500000, 0x00000000a4500000, 0x00000000a4600000|  0%| F|  |TAMS 0x00000000a4500000, 0x00000000a4500000| Untracked 
|  56|0x00000000a4600000, 0x00000000a46ff1a0, 0x00000000a4700000| 99%| E|  |TAMS 0x00000000a4600000, 0x00000000a4600000| Complete 
|  57|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| E|CS|TAMS 0x00000000a4700000, 0x00000000a4700000| Complete 
|  58|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| E|CS|TAMS 0x00000000a4800000, 0x00000000a4800000| Complete 
|  59|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| E|CS|TAMS 0x00000000a4900000, 0x00000000a4900000| Complete 
|  60|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| E|CS|TAMS 0x00000000a4a00000, 0x00000000a4a00000| Complete 
|  61|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| E|CS|TAMS 0x00000000a4b00000, 0x00000000a4b00000| Complete 
|  62|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| E|CS|TAMS 0x00000000a4c00000, 0x00000000a4c00000| Complete 
|  63|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| E|CS|TAMS 0x00000000a4d00000, 0x00000000a4d00000| Complete 
|  64|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| E|CS|TAMS 0x00000000a4e00000, 0x00000000a4e00000| Complete 
|  65|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| E|CS|TAMS 0x00000000a4f00000, 0x00000000a4f00000| Complete 
|  66|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| E|CS|TAMS 0x00000000a5000000, 0x00000000a5000000| Complete 
|  67|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| E|CS|TAMS 0x00000000a5100000, 0x00000000a5100000| Complete 
|  68|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| E|CS|TAMS 0x00000000a5200000, 0x00000000a5200000| Complete 
|  69|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| E|CS|TAMS 0x00000000a5300000, 0x00000000a5300000| Complete 
|  70|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| E|CS|TAMS 0x00000000a5400000, 0x00000000a5400000| Complete 
|  71|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| E|CS|TAMS 0x00000000a5500000, 0x00000000a5500000| Complete 
|  72|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| E|CS|TAMS 0x00000000a5600000, 0x00000000a5600000| Complete 
|  95|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| E|CS|TAMS 0x00000000a6d00000, 0x00000000a6d00000| Complete 

Card table byte_map: [0x00000200a99a0000,0x00000200a9ca0000] _byte_map_base: 0x00000200a9499000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002009279d110, (CMBitMap*) 0x000002009279d0d0
 Prev Bits: [0x00000200ab770000, 0x00000200acf38000)
 Next Bits: [0x00000200a9fa0000, 0x00000200ab768000)

Polling page: 0x0000020090e80000

Metaspace:

Usage:
  Non-class:     30.30 MB used.
      Class:      4.29 MB used.
       Both:     34.59 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      30.50 MB ( 48%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      34.94 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  1.03 MB
       Class:  11.55 MB
        Both:  12.58 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.31 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 366.
num_arena_deaths: 2.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 559.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 5.
num_chunks_taken_from_freelist: 1338.
num_chunk_merges: 3.
num_chunk_splits: 967.
num_chunks_enlarged: 752.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1886Kb max_used=1886Kb free=118113Kb
 bounds [0x00000200a1b40000, 0x00000200a1db0000, 0x00000200a9070000]
CodeHeap 'profiled nmethods': size=120000Kb used=7644Kb max_used=7644Kb free=112356Kb
 bounds [0x000002009a070000, 0x000002009a7f0000, 0x00000200a15a0000]
CodeHeap 'non-nmethods': size=5760Kb used=1318Kb max_used=1357Kb free=4441Kb
 bounds [0x00000200a15a0000, 0x00000200a1810000, 0x00000200a1b40000]
 total_blobs=4216 nmethods=3573 adapters=555
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 16.288 Thread 0x00000200af4fd350 nmethod 3549 0x000002009a7d2510 code [0x000002009a7d2720, 0x000002009a7d2d88]
Event: 16.288 Thread 0x00000200af4fd350 3550       3       com.sun.org.apache.xerces.internal.impl.XMLEntityManager$RewindableInputStream::readAndBuffer (97 bytes)
Event: 16.289 Thread 0x00000200af4fd350 nmethod 3550 0x000002009a7d2f10 code [0x000002009a7d30e0, 0x000002009a7d3578]
Event: 16.290 Thread 0x00000200af4fd350 3551       3       com.sun.org.apache.xerces.internal.util.SymbolTable$Entry::<init> (47 bytes)
Event: 16.290 Thread 0x00000200af4fd350 nmethod 3551 0x000002009a7d3710 code [0x000002009a7d38e0, 0x000002009a7d3da8]
Event: 16.291 Thread 0x00000200af4fd350 3552       3       com.sun.org.apache.xerces.internal.util.NamespaceSupport::popContext (21 bytes)
Event: 16.291 Thread 0x00000200af4fd350 nmethod 3552 0x000002009a7d3f10 code [0x000002009a7d40a0, 0x000002009a7d41d8]
Event: 16.292 Thread 0x00000200af4f8880 3553       4       com.sun.org.apache.xerces.internal.util.SymbolTable::hash (53 bytes)
Event: 16.294 Thread 0x00000200af4fd350 3554       3       jdk.internal.loader.BuiltinClassLoader$2::run (5 bytes)
Event: 16.295 Thread 0x00000200af4fd350 nmethod 3554 0x000002009a7d4290 code [0x000002009a7d4440, 0x000002009a7d4588]
Event: 16.299 Thread 0x00000200af4f8880 nmethod 3553 0x00000200a1d15410 code [0x00000200a1d15580, 0x00000200a1d15778]
Event: 16.299 Thread 0x00000200af4f8880 3555       4       jdk.internal.jimage.ImageLocation::verify (218 bytes)
Event: 16.307 Thread 0x00000200af4fd350 3556       3       java.util.Collections::unmodifiableSet (20 bytes)
Event: 16.307 Thread 0x00000200af4fd350 nmethod 3556 0x000002009a7d4610 code [0x000002009a7d47e0, 0x000002009a7d4c48]
Event: 16.307 Thread 0x00000200af4fd350 3557       3       java.util.Collections$UnmodifiableSet::<init> (6 bytes)
Event: 16.308 Thread 0x00000200af4fd350 nmethod 3557 0x000002009a7d4e10 code [0x000002009a7d4fc0, 0x000002009a7d52e8]
Event: 16.356 Thread 0x00000200f5ce7ff0 nmethod 3542 0x00000200a1d15890 code [0x00000200a1d15a40, 0x00000200a1d16e58]
Event: 16.364 Thread 0x00000200af4fd350 3558       3       org.apache.xmlbeans.impl.schema.SchemaTypeImpl::assertUnresolved (23 bytes)
Event: 16.365 Thread 0x00000200af4fd350 nmethod 3558 0x000002009a7d5410 code [0x000002009a7d55c0, 0x000002009a7d5808]
Event: 16.372 Thread 0x00000200f5ce7ff0 3559       4       jdk.internal.module.SystemModuleFinders$SystemModuleReader::find (59 bytes)

GC Heap History (16 events):
Event: 1.282 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 98304K, used 14336K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 0 survivors (0K)
 Metaspace       used 925K, committed 1088K, reserved 1114112K
  class space    used 61K, committed 128K, reserved 1048576K
}
Event: 1.290 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 98304K, used 9200K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 925K, committed 1088K, reserved 1114112K
  class space    used 61K, committed 128K, reserved 1048576K
}
Event: 1.980 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 98304K, used 18416K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 4047K, committed 4224K, reserved 1114112K
  class space    used 436K, committed 512K, reserved 1048576K
}
Event: 1.985 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 98304K, used 10842K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 4047K, committed 4224K, reserved 1114112K
  class space    used 436K, committed 512K, reserved 1048576K
}
Event: 3.601 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 98304K, used 30298K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 2 survivors (2048K)
 Metaspace       used 10135K, committed 10368K, reserved 1114112K
  class space    used 1170K, committed 1280K, reserved 1048576K
}
Event: 3.613 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 98304K, used 13465K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 10135K, committed 10368K, reserved 1114112K
  class space    used 1170K, committed 1280K, reserved 1048576K
}
Event: 5.775 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 98304K, used 47257K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 3 survivors (3072K)
 Metaspace       used 13856K, committed 14080K, reserved 1114112K
  class space    used 1612K, committed 1728K, reserved 1048576K
}
Event: 5.797 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 98304K, used 19328K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 13856K, committed 14080K, reserved 1114112K
  class space    used 1612K, committed 1728K, reserved 1048576K
}
Event: 7.170 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 98304K, used 41856K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 5 survivors (5120K)
 Metaspace       used 21228K, committed 21504K, reserved 1114112K
  class space    used 2544K, committed 2688K, reserved 1048576K
}
Event: 7.186 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 98304K, used 21504K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 21228K, committed 21504K, reserved 1114112K
  class space    used 2544K, committed 2688K, reserved 1048576K
}
Event: 13.382 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 75776K, used 60416K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 37 young (37888K), 2 survivors (2048K)
 Metaspace       used 28980K, committed 29376K, reserved 1114112K
  class space    used 3578K, committed 3776K, reserved 1048576K
}
Event: 13.394 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 75776K, used 28672K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 28980K, committed 29376K, reserved 1114112K
  class space    used 3578K, committed 3776K, reserved 1048576K
}
Event: 15.161 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 75776K, used 53248K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 4 survivors (4096K)
 Metaspace       used 33543K, committed 33856K, reserved 1114112K
  class space    used 4152K, committed 4288K, reserved 1048576K
}
Event: 15.175 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 75776K, used 30007K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 33543K, committed 33856K, reserved 1114112K
  class space    used 4152K, committed 4288K, reserved 1048576K
}
Event: 15.344 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 75776K, used 54583K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 2 survivors (2048K)
 Metaspace       used 33543K, committed 33856K, reserved 1114112K
  class space    used 4152K, committed 4288K, reserved 1048576K
}
Event: 15.354 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 75776K, used 30675K [0x00000000a0e00000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 33543K, committed 33856K, reserved 1114112K
  class space    used 4152K, committed 4288K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 15.764 Thread 0x0000020092751730 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000200a1d08300 relative=0x0000000000000780
Event: 15.764 Thread 0x0000020092751730 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000200a1d08300 method=org.apache.xmlbeans.impl.schema.SchemaTypeSystemImpl$StringPool.stringForCode(I)Ljava/lang/String; @ 1 c2
Event: 15.764 Thread 0x0000020092751730 DEOPT PACKING pc=0x00000200a1d08300 sp=0x000000490f9fc830
Event: 15.764 Thread 0x0000020092751730 DEOPT UNPACKING pc=0x00000200a15f23a3 sp=0x000000490f9fc6f8 mode 2
Event: 16.246 Thread 0x0000020092751730 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000200a1d10f8c relative=0x00000000000017cc
Event: 16.246 Thread 0x0000020092751730 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000200a1d10f8c method=jdk.internal.jimage.ImageStringsReader.stringFromByteBufferMatches(Ljava/nio/ByteBuffer;ILjava/lang/String;I)I @ 19 c2
Event: 16.246 Thread 0x0000020092751730 DEOPT PACKING pc=0x00000200a1d10f8c sp=0x000000490f9fc430
Event: 16.246 Thread 0x0000020092751730 DEOPT UNPACKING pc=0x00000200a15f23a3 sp=0x000000490f9fc188 mode 2
Event: 16.246 Thread 0x0000020092751730 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000200a1d10f8c relative=0x00000000000017cc
Event: 16.252 Thread 0x0000020092751730 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000200a1d10f8c method=jdk.internal.jimage.ImageStringsReader.stringFromByteBufferMatches(Ljava/nio/ByteBuffer;ILjava/lang/String;I)I @ 19 c2
Event: 16.252 Thread 0x0000020092751730 DEOPT PACKING pc=0x00000200a1d10f8c sp=0x000000490f9fc430
Event: 16.252 Thread 0x0000020092751730 DEOPT UNPACKING pc=0x00000200a15f23a3 sp=0x000000490f9fc188 mode 2
Event: 16.252 Thread 0x0000020092751730 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000200a1d10f8c relative=0x00000000000017cc
Event: 16.252 Thread 0x0000020092751730 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000200a1d10f8c method=jdk.internal.jimage.ImageStringsReader.stringFromByteBufferMatches(Ljava/nio/ByteBuffer;ILjava/lang/String;I)I @ 19 c2
Event: 16.252 Thread 0x0000020092751730 DEOPT PACKING pc=0x00000200a1d10f8c sp=0x000000490f9fc430
Event: 16.252 Thread 0x0000020092751730 DEOPT UNPACKING pc=0x00000200a15f23a3 sp=0x000000490f9fc188 mode 2
Event: 16.252 Thread 0x0000020092751730 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000200a1d10f8c relative=0x00000000000017cc
Event: 16.252 Thread 0x0000020092751730 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000200a1d10f8c method=jdk.internal.jimage.ImageStringsReader.stringFromByteBufferMatches(Ljava/nio/ByteBuffer;ILjava/lang/String;I)I @ 19 c2
Event: 16.252 Thread 0x0000020092751730 DEOPT PACKING pc=0x00000200a1d10f8c sp=0x000000490f9fc430
Event: 16.252 Thread 0x0000020092751730 DEOPT UNPACKING pc=0x00000200a15f23a3 sp=0x000000490f9fc188 mode 2

Classes unloaded (1 events):
Event: 7.213 Thread 0x00000200af4da6d0 Unloading class 0x00000200b1002000 'java/lang/invoke/LambdaForm$DMH+0x00000200b1002000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 6.830 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a5d22170}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000a5d22170) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 6.903 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a5c884c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, int)'> (0x00000000a5c884c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 6.904 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a5c93210}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000a5c93210) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.045 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a5915d38}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a5915d38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.240 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a6db9470}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a6db9470) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.182 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4cc8420}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a4cc8420) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.418 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4a0f458}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a4a0f458) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.602 Thread 0x0000020092751730 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a48fb410}: sun/util/logging/resources/spi/loggingProvider> (0x00000000a48fb410) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 12.585 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a3f36938}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a3f36938) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.117 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a3631e08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long, int, java.lang.Object)'> (0x00000000a3631e08) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.307 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a33901c8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a33901c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.344 Thread 0x0000020092751730 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000a33c1020}: Found class java.lang.Object, but interface was expected> (0x00000000a33c1020) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 13.723 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a525a080}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a525a080) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.741 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a528c4d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000a528c4d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.742 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a528fc40}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000a528fc40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.904 Thread 0x0000020092751730 Implicit null exception at 0x00000200a1c3bbd8 to 0x00000200a1c3bc7c
Event: 13.933 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4f11238}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a4f11238) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.935 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4f17540}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x00000000a4f17540) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.935 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4f1ae88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int)'> (0x00000000a4f1ae88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.973 Thread 0x0000020092751730 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a4503d10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000a4503d10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 12.622 Executing VM operation: Cleanup
Event: 12.622 Executing VM operation: Cleanup done
Event: 13.130 Executing VM operation: HandshakeAllThreads
Event: 13.130 Executing VM operation: HandshakeAllThreads done
Event: 13.352 Executing VM operation: HandshakeAllThreads
Event: 13.352 Executing VM operation: HandshakeAllThreads done
Event: 13.382 Executing VM operation: G1CollectForAllocation
Event: 13.394 Executing VM operation: G1CollectForAllocation done
Event: 13.898 Executing VM operation: HandshakeAllThreads
Event: 13.898 Executing VM operation: HandshakeAllThreads done
Event: 14.903 Executing VM operation: Cleanup
Event: 14.904 Executing VM operation: Cleanup done
Event: 14.966 Executing VM operation: HandshakeAllThreads
Event: 14.966 Executing VM operation: HandshakeAllThreads done
Event: 15.161 Executing VM operation: G1CollectForAllocation
Event: 15.175 Executing VM operation: G1CollectForAllocation done
Event: 15.344 Executing VM operation: G1CollectForAllocation
Event: 15.354 Executing VM operation: G1CollectForAllocation done
Event: 16.363 Executing VM operation: Cleanup
Event: 16.363 Executing VM operation: Cleanup done

Events (20 events):
Event: 14.966 loading class java/io/UTFDataFormatException done
Event: 15.008 Thread 0x00000200f6a243f0 Thread added: 0x00000200f6a243f0
Event: 15.491 loading class org/w3c/dom/DOMImplementation
Event: 15.491 loading class org/w3c/dom/DOMImplementation done
Event: 15.512 loading class org/xml/sax/ext/LexicalHandler
Event: 15.512 loading class org/xml/sax/ext/LexicalHandler done
Event: 15.512 loading class org/xml/sax/ext/DeclHandler
Event: 15.512 loading class org/xml/sax/ext/DeclHandler done
Event: 15.527 loading class org/w3c/dom/CDATASection
Event: 15.527 loading class org/w3c/dom/CDATASection done
Event: 15.563 loading class java/lang/AbstractMethodError
Event: 15.563 loading class java/lang/AbstractMethodError done
Event: 15.570 Thread 0x00000200f6a243f0 Thread exited: 0x00000200f6a243f0
Event: 15.583 loading class org/w3c/dom/Comment
Event: 15.583 loading class org/w3c/dom/Comment done
Event: 15.591 loading class org/w3c/dom/ProcessingInstruction
Event: 15.591 loading class org/w3c/dom/ProcessingInstruction done
Event: 15.597 loading class org/w3c/dom/DocumentFragment
Event: 15.597 loading class org/w3c/dom/DocumentFragment done
Event: 16.212 Thread 0x00000200f5ce7ff0 Thread added: 0x00000200f5ce7ff0


Dynamic libraries:
0x00007ff627770000 - 0x00007ff627780000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffa8c100000 - 0x00007ffa8c366000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa89ec0000 - 0x00007ffa89f89000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa89af0000 - 0x00007ffa89ebc000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa899a0000 - 0x00007ffa89aeb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa7b430000 - 0x00007ffa7b44b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffa7b5b0000 - 0x00007ffa7b5c9000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffa8a470000 - 0x00007ffa8a522000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa8bc30000 - 0x00007ffa8bcd9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa8a130000 - 0x00007ffa8a1d6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa8a5f0000 - 0x00007ffa8a706000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa77550000 - 0x00007ffa777ea000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffa8bd10000 - 0x00007ffa8beda000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa89480000 - 0x00007ffa894a7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa82540000 - 0x00007ffa8254b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa8a250000 - 0x00007ffa8a27b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa89860000 - 0x00007ffa89992000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa894b0000 - 0x00007ffa89553000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa8a530000 - 0x00007ffa8a560000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa7ccc0000 - 0x00007ffa7cccc000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffa55d50000 - 0x00007ffa55dde000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ffa3d2f0000 - 0x00007ffa3ded0000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffa8a240000 - 0x00007ffa8a248000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa6bab0000 - 0x00007ffa6baba000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82500000 - 0x00007ffa82536000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa8a980000 - 0x00007ffa8a9f4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa88050000 - 0x00007ffa8806a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa7cb50000 - 0x00007ffa7cb5a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffa7faa0000 - 0x00007ffa7fce1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa8aa90000 - 0x00007ffa8ae14000 	C:\WINDOWS\System32\combase.dll
0x00007ffa8ae30000 - 0x00007ffa8af10000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa6fe40000 - 0x00007ffa6fe79000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa89290000 - 0x00007ffa89329000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa83690000 - 0x00007ffa8369e000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ffa7b470000 - 0x00007ffa7b495000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffa55c70000 - 0x00007ffa55d47000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ffa8b500000 - 0x00007ffa8bc2d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa896e0000 - 0x00007ffa89854000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa86ef0000 - 0x00007ffa87746000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa8a280000 - 0x00007ffa8a36f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa8a380000 - 0x00007ffa8a3e9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa891a0000 - 0x00007ffa891cf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa785c0000 - 0x00007ffa785d8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ffa7b410000 - 0x00007ffa7b429000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ffa7f740000 - 0x00007ffa7f85e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa885c0000 - 0x00007ffa8862a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa7b3f0000 - 0x00007ffa7b406000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ffa7cd70000 - 0x00007ffa7cd8a000 	C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\bin\breakgen64.dll
0x00007ffa7b570000 - 0x00007ffa7b580000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ffa88870000 - 0x00007ffa8888c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa87fb0000 - 0x00007ffa87fea000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa88660000 - 0x00007ffa8868b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa89170000 - 0x00007ffa89196000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa88860000 - 0x00007ffa8886c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa87aa0000 - 0x00007ffa87ad3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa8bce0000 - 0x00007ffa8bcea000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7f710000 - 0x00007ffa7f72f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7f6a0000 - 0x00007ffa7f6c5000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa87b40000 - 0x00007ffa87c67000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa7b270000 - 0x00007ffa7b27b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa7e600000 - 0x00007ffa7e686000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffa7ccd0000 - 0x00007ffa7ccda000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ffa7b5a0000 - 0x00007ffa7b5ab000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ffa7b460000 - 0x00007ffa7b46e000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ffa89560000 - 0x00007ffa896d7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa88a70000 - 0x00007ffa88aa0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa88a20000 - 0x00007ffa88a5f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa7b450000 - 0x00007ffa7b458000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffa6e3d0000 - 0x00007ffa6e3e8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffa6c780000 - 0x00007ffa6c792000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffa6c740000 - 0x00007ffa6c770000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffa6c710000 - 0x00007ffa6c730000 	C:\WINDOWS\system32\wshbth.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Java\jdk-17\bin\server;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\bin

VM Arguments:
jvm_args: -ea -DBROWSER=CHROME -Didea.test.cyclic.buffer.size=1048576 -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\lib\idea_rt.jar=64777:C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\bin -Dfile.encoding=UTF-8 
java_command: com.intellij.rt.testng.RemoteTestNGStarter -usedefaultlisteners false -socket64776 @w@C:\Users\<USER>\AppData\Local\Temp\idea_working_dirs_testng.tmp -temp C:\Users\<USER>\AppData\Local\Temp\idea_testng.tmp
java_class_path (initial): C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\lib\idea_rt.jar;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.1.2\plugins\testng\lib\testng-rt.jar;C:\Users\<USER>\Downloads\demo (1)\Automation-Framework\target\test-classes;C:\Users\<USER>\Downloads\demo (1)\Automation-Framework\target\classes;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-java\4.11.0\selenium-java-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-api\4.11.0\selenium-api-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-chrome-driver\4.11.0\selenium-chrome-driver-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-chromium-driver\4.11.0\selenium-chromium-driver-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v113\4.11.0\selenium-devtools-v113-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v114\4.11.0\selenium-devtools-v114-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v115\4.11.0\selenium-devtools-v115-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-devtools-v85\4.11.0\selenium-devtools-v85-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-edge-driver\4.11.0\selenium-edge-driver-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-ie-driver\4.11.0\selenium-ie-driver-4.11.0.jar;C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-remote-driver\4.11.0\selenium-remote-driver-4.11.0.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.92.Final\netty-buffer-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.92.Final\netty-codec-http-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.92.Final\netty-codec-4.1.92.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.92.Final\netty-handler-4.1.92.Final.jar;C:\Users\<USER>\.m2\re
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 100663296                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1595932672                                {product} {ergonomic}
   size_t MaxNewSize                               = 957349888                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1595932672                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Program Files\Java\jdk-17\bin;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\PostgreSQL\16\bin;C:\Program Files\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\Program Files\Git\cmd;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2022.2.4\bin;;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.2.4\bin;;C:\Program Files\JetBrains\PyCharm Community Edition 2023.3.3\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR;C:\spark-3.4.0-bin-hadoop3\spark-3.4.0-bin-hadoop3\bin;C:\spark-3.4.0-bin-hadoop3\hadoop\bin;C:\spark-3.4.0-bin-hadoop3\hadoop;C:\Program Files\Azure Data Studio\bin;C:\Program Files\JetBrains\PyCharm 2023.3.3\bin;;C:\Program Files\Java\jdk-17\lib;C:\Program Files\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;
USERNAME=Asus
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 11 days 5:48 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (4 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x8108109, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 6081M (409M free)
TotalPageFile size 24513M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 194M, peak: 201M
current process commit charge ("private bytes"): 237M, peak: 243M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207) for windows-amd64 JRE (17.0.11+7-LTS-207), built on Mar 11 2024 19:01:50 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
