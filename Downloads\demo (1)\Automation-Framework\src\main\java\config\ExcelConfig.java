package config;

/**
 * Configuration class for Excel file structure
 * Modify these values to match your actual Excel structure
 */
public class ExcelConfig {
    
    // Excel file settings
    public static final String EXCEL_FILE_PATH = "src/resources/testdata/FieldInputSheet.xlsx";
    public static final String SHEET_NAME = "Sheet1";  // Change this if your sheet has a different name
    
    // Column indices (1-based) - UPDATE THESE TO MATCH YOUR EXCEL STRUCTURE
    public static final int TEST_CASE_ID_COLUMN = 1;     // Column A: TC001, TC002, etc.
    public static final int TEST_CASE_NAME_COLUMN = 2;   // Column B: Test case name
    public static final int STEP_DESCRIPTION_COLUMN = 6; // Column F: Step description (1. Enter Email, 2. Enter Password, etc.)
    public static final int TEST_DATA_COLUMN = 9;        // Column I: Test data (<EMAIL>, <PERSON><PERSON><PERSON>@123, etc.)
    public static final int XPATH_COLUMN = 13;           // Column M: XPath (//*[@id="email"], //*[@id="password"]/div/input, etc.)
    
    /**
     * Alternative sheet names to try if the default doesn't work
     */
    public static final String[] POSSIBLE_SHEET_NAMES = {
        "Sheet1",
        "Sheet 1", 
        "TestCases",
        "Test Cases",
        "Data",
        "Main"
    };
    
    /**
     * Debug method to print current configuration
     */
    public static void printConfiguration() {
        System.out.println("📋 Excel Configuration:");
        System.out.println("   File: " + EXCEL_FILE_PATH);
        System.out.println("   Sheet: " + SHEET_NAME);
        System.out.println("   Test Case ID Column: " + TEST_CASE_ID_COLUMN + " (Column " + getColumnLetter(TEST_CASE_ID_COLUMN) + ")");
        System.out.println("   Step Description Column: " + STEP_DESCRIPTION_COLUMN + " (Column " + getColumnLetter(STEP_DESCRIPTION_COLUMN) + ")");
        System.out.println("   Test Data Column: " + TEST_DATA_COLUMN + " (Column " + getColumnLetter(TEST_DATA_COLUMN) + ")");
        System.out.println("   XPath Column: " + XPATH_COLUMN + " (Column " + getColumnLetter(XPATH_COLUMN) + ")");
    }
    
    /**
     * Converts column number to letter (1=A, 2=B, etc.)
     */
    private static String getColumnLetter(int columnNumber) {
        String[] columns = {"", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T"};
        return columnNumber < columns.length ? columns[columnNumber] : "Col" + columnNumber;
    }
}

/**
 * HOW TO FIX TC001 ISSUES:
 * 
 * 1. Open your Excel file: src/resources/testdata/FieldInputSheet.xlsx
 * 
 * 2. Find where TC001 is located:
 *    - If TC001 is in Column B (not A), change TEST_CASE_ID_COLUMN = 2
 *    - If TC001 is in Column C, change TEST_CASE_ID_COLUMN = 3
 *    - etc.
 * 
 * 3. Find where your XPaths are located:
 *    - If XPaths are in Column N (not M), change XPATH_COLUMN = 14
 *    - If XPaths are in Column L, change XPATH_COLUMN = 12
 *    - etc.
 * 
 * 4. Check your sheet name:
 *    - If your sheet is named "Sheet 1" (with space), change SHEET_NAME = "Sheet 1"
 *    - If your sheet has a different name, update SHEET_NAME accordingly
 * 
 * 5. Save this file and recompile: mvn compile
 * 
 * EXAMPLE FIXES:
 * 
 * // If TC001 is in Column B and XPaths are in Column N:
 * public static final int TEST_CASE_ID_COLUMN = 2;  // Column B
 * public static final int XPATH_COLUMN = 14;        // Column N
 * 
 * // If your sheet is named "Test Cases":
 * public static final String SHEET_NAME = "Test Cases";
 */
