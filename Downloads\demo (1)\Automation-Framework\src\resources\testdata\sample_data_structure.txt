Sample Excel File Structure for Product Validation

=== LLC.xlsx ===
Sheet Name: "Entity Formation"
Structure:
Row 1 (Header): Product Name | Alabama | Alaska | Arizona | Arkansas | California | ...
Row 2: Basic LLC Formation | Yes | Yes | No | Yes | Yes | ...
Row 3: Expedited LLC Formation | Yes | No | Yes | Yes | Yes | ...
Row 4: Registered Agent Service | Yes | Yes | Yes | Yes | Yes | ...
Row 5: EIN Service | Yes | Yes | Yes | Yes | Yes | ...

Sheet Name: "Compliance"
Structure:
Row 1 (Header): Product Name | Alabama | Alaska | Arizona | Arkansas | California | ...
Row 2: Annual Report Filing | Yes | Yes | No | Yes | Yes | ...
Row 3: State Tax Registration | Yes | No | Yes | Yes | Yes | ...

=== Corp.xlsx ===
Sheet Name: "Entity Formation"
Structure:
Row 1 (Header): Product Name | Alabama | Alaska | Arizona | Arkansas | California | ...
Row 2: Basic Corporation Formation | Yes | Yes | No | Yes | Yes | ...
Row 3: Expedited Corporation Formation | Yes | No | Yes | Yes | Yes | ...
Row 4: Registered Agent Service | Yes | Yes | Yes | Yes | Yes | ...

Instructions for creating actual Excel files:
1. Create LLC.xlsx and Corp.xlsx in src/resources/testdata/
2. Each file should have sheets named after categories (e.g., "Entity Formation", "Compliance")
3. First column should contain product names
4. Subsequent columns should contain state names as headers
5. Cell values should contain "Yes", "No", or actual product names
6. For multiple products in one cell, separate with commas

Example cell content:
- "Basic LLC Formation, Expedited LLC Formation"
- "Registered Agent Service"
- "EIN Service, State Tax Registration"
