package examples;

import drivers.DriverManager;
import models.ProductComparisonResult;
import pages.ProductFilterPage;
import pages.ProductValidator;
import utils.TestCaseExecutor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Example class showing how to use dynamic XPath configuration
 * for different applications and UI structures
 */
public class DynamicXPathExample {

    public static void main(String[] args) {
        System.out.println("🎯 Dynamic XPath Configuration Example");
        System.out.println("=" + "=".repeat(50));

        try {
            // Initialize WebDriver
            DriverManager.initDriver();
            
            // Example 1: Using default XPaths
            example1_DefaultXPaths();
            
            // Example 2: Setting custom XPaths at runtime
            example2_CustomXPaths();
            
            // Example 3: Using custom product XPath
            example3_CustomProductXPath();
            
            // Example 4: Complete dynamic validation
            example4_CompleteValidation();
            
        } catch (Exception e) {
            System.err.println("❌ Example failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
        }
    }

    /**
     * Example 1: Using default XPaths (backward compatibility)
     */
    private static void example1_DefaultXPaths() {
        System.out.println("\n📋 Example 1: Using Default XPaths");
        System.out.println("-".repeat(40));
        
        try {
            // Login first
            TestCaseExecutor testExecutor = new TestCaseExecutor();
            testExecutor.executeTestCase("TC001");
            
            // Use ProductValidator with default XPaths
            ProductValidator validator = new ProductValidator();
            ProductComparisonResult result = validator.validateProductsForCategory(
                "LLC", "Alaska", "Entity Formation", null
            );
            
            System.out.println("✅ Default XPath validation completed");
            System.out.println("Result: " + (result.isTestPassed() ? "PASSED" : "FAILED"));
            
        } catch (Exception e) {
            System.err.println("❌ Example 1 failed: " + e.getMessage());
        }
    }

    /**
     * Example 2: Setting custom XPaths at runtime
     */
    private static void example2_CustomXPaths() {
        System.out.println("\n📋 Example 2: Setting Custom XPaths");
        System.out.println("-".repeat(40));
        
        try {
            // Create ProductFilterPage with custom XPaths
            ProductFilterPage filterPage = new ProductFilterPage(DriverManager.getDriver());
            
            // Set custom XPaths for your specific application
            filterPage.setCustomXPath("category", "//select[@id='category-dropdown']");
            filterPage.setCustomXPath("entityType", "//select[@name='entity_type']");
            filterPage.setCustomXPath("state", "//div[@class='state-selector']//button");
            
            // Or set multiple XPaths at once
            Map<String, String> customXPaths = new HashMap<>();
            customXPaths.put("category", "//div[@data-testid='category-select']");
            customXPaths.put("entityType", "//div[@data-testid='entity-select']");
            customXPaths.put("state", "//div[@data-testid='state-select']");
            
            filterPage.setCustomXPaths(customXPaths);
            
            System.out.println("✅ Custom XPaths configured successfully");
            
        } catch (Exception e) {
            System.err.println("❌ Example 2 failed: " + e.getMessage());
        }
    }

    /**
     * Example 3: Using custom product XPath
     */
    private static void example3_CustomProductXPath() {
        System.out.println("\n📋 Example 3: Custom Product XPath");
        System.out.println("-".repeat(40));
        
        try {
            ProductFilterPage filterPage = new ProductFilterPage(DriverManager.getDriver());
            
            // Apply filters first (using default or custom XPaths)
            filterPage.applyFilters("Entity Formation", "LLC", "Alaska", null);
            
            // Capture products using custom XPath
            String customProductXPath = "//div[@class='product-grid']//div[@class='product-card']//h3";
            List<String> products = filterPage.captureProductsWithCustomXPath(customProductXPath);
            
            System.out.println("✅ Captured " + products.size() + " products using custom XPath");
            System.out.println("Products: " + products);
            
        } catch (Exception e) {
            System.err.println("❌ Example 3 failed: " + e.getMessage());
        }
    }

    /**
     * Example 4: Complete dynamic validation with custom XPaths
     */
    private static void example4_CompleteValidation() {
        System.out.println("\n📋 Example 4: Complete Dynamic Validation");
        System.out.println("-".repeat(40));
        
        try {
            ProductFilterPage filterPage = new ProductFilterPage(DriverManager.getDriver());
            
            // Apply filters with completely custom XPaths
            filterPage.applyFiltersWithCustomXPaths(
                "Entity Formation",  // category
                "LLC",              // entityType
                "Alaska",           // state
                null,               // county
                "//div[@id='category-filter']//button",     // categoryXPath
                "//div[@id='entity-filter']//select",       // entityTypeXPath
                "//div[@id='state-filter']//input",         // stateXPath
                null                                         // countyXPath
            );
            
            // Capture products with custom XPath
            String productXPath = "//div[@class='results-panel']//div[@class='product-item']//span[@class='product-name']";
            List<String> actualProducts = filterPage.captureProductsWithCustomXPath(productXPath);
            
            System.out.println("✅ Complete dynamic validation completed");
            System.out.println("Found " + actualProducts.size() + " products: " + actualProducts);
            
        } catch (Exception e) {
            System.err.println("❌ Example 4 failed: " + e.getMessage());
        }
    }
}

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. **For Your Application**: Update the XPaths in the examples above to match your UI
 * 
 * 2. **Common XPath Patterns**:
 *    - Dropdown: "//select[@id='dropdown-id']"
 *    - Button: "//button[@class='dropdown-button']"
 *    - Div with click: "//div[@data-testid='selector']"
 *    - Products: "//div[@class='product-list']//div[@class='product-item']"
 * 
 * 3. **Integration with Test Cases**:
 *    You can store XPaths in Excel and read them dynamically:
 *    
 *    | Test Case | Category XPath | Entity XPath | State XPath | Product XPath |
 *    |-----------|----------------|--------------|-------------|---------------|
 *    | TC001     | //select[@id='cat'] | //select[@id='entity'] | //select[@id='state'] | //div[@class='products']//span |
 * 
 * 4. **Runtime Configuration**:
 *    ```java
 *    // Read XPaths from Excel or config file
 *    String categoryXPath = excelUtils.getCellData("config.xlsx", "XPaths", 1, 2);
 *    filterPage.setCustomXPath("category", categoryXPath);
 *    ```
 * 
 * 5. **Environment-Specific XPaths**:
 *    ```java
 *    // Different XPaths for different environments
 *    if (environment.equals("staging")) {
 *        filterPage.setCustomXPath("category", "//div[@data-test='category-staging']");
 *    } else {
 *        filterPage.setCustomXPath("category", "//div[@data-test='category-prod']");
 *    }
 *    ```
 */
