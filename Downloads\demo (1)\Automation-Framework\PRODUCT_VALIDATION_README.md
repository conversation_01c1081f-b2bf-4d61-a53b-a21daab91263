# Product Validation Test Flow

This document explains the comprehensive product validation test flow that compares expected products from Excel files with actual products displayed on the UI.

## 🔁 Test Flow Overview

The test flow follows these steps:

1. **Read Expected Data from Excel** - Opens LLC.xlsx or Corp.xlsx and extracts expected products for a specific state and category
2. **Apply Filters on UI** - Selects Category, Entity Type, State, and optionally County
3. **Capture Actual UI Products** - Scrapes visible product names from the UI after filters are applied
4. **Compare UI vs Excel** - Identifies matched, missing, and extra products
5. **Output Result Log** - Generates detailed comparison results

## 📁 File Structure

```
src/
├── main/java/
│   ├── models/
│   │   ├── ProductComparisonResult.java    # Result model with comparison logic
│   │   └── TestData.java                   # Test data model
│   ├── pages/
│   │   ├── ProductValidator.java           # Main validation class
│   │   └── ProductFilterPage.java          # UI filter operations
│   ├── utils/
│   │   ├── ProductDataReader.java          # Excel data reader
│   │   └── ExcelFileCreator.java           # Utility to create sample Excel files
│   └── utiles/
│       └── ExcelUtils.java                 # Enhanced Excel utilities
├── test/java/
│   └── tests/
│       └── ProductValidationTest.java      # Test class with examples
└── resources/testdata/
    ├── LLC.xlsx                            # Expected products for LLC
    ├── Corp.xlsx                           # Expected products for Corp
    └── sample_data_structure.txt           # Excel structure guide
```

## 🚀 Quick Start

### 1. Create Sample Excel Files

Run the ExcelFileCreator to generate sample data:

```java
// Run this main method to create sample Excel files
utils.ExcelFileCreator.main(new String[]{});
```

This creates:
- `LLC.xlsx` with "Entity Formation" and "Compliance" sheets
- `Corp.xlsx` with "Entity Formation" and "Compliance" sheets

### 2. Run Basic Validation

```java
// Initialize validator
ProductValidator validator = new ProductValidator();

// Test specific combination
ProductComparisonResult result = validator.validateProductsForCategory(
    "LLC",              // Entity Type
    "Alaska",           // State
    "Entity Formation", // Category
    null               // County (optional)
);

// Check results
System.out.println(result.getResultSummary());
boolean passed = result.isTestPassed();
```

### 3. Run Complete Flow

```java
// This tests all categories for the given entity type and state
validator.dynamicOrder("Alaska", "LLC");
```

## 📊 Excel File Structure

### Required Structure:
- **File Names**: `LLC.xlsx`, `Corp.xlsx`
- **Sheet Names**: Category names (e.g., "Entity Formation", "Compliance")
- **Column A**: Product names
- **Columns B+**: State names as headers
- **Cell Values**: Product names or "Yes"/"No" indicators

### Example:
```
| Product Name              | Alabama | Alaska | Arizona |
|---------------------------|---------|--------|---------|
| Basic LLC Formation       | Yes     | Yes    |         |
| Expedited LLC Formation   | Yes     |        | Yes     |
| Registered Agent Service  | Yes     | Yes    | Yes     |
```

## 🎯 Usage Examples

### Example 1: Single Category Test
```java
ProductValidator validator = new ProductValidator();
ProductComparisonResult result = validator.validateProductsForCategory(
    "LLC", "California", "Entity Formation", null
);

if (result.isTestPassed()) {
    System.out.println("✅ All products match!");
} else {
    System.out.println("❌ Mismatches found:");
    System.out.println("Missing: " + result.getMissingProducts());
    System.out.println("Extra: " + result.getExtraProducts());
}
```

### Example 2: Quick Validation
```java
boolean passed = validator.quickValidate("Corp", "Texas", "Entity Formation");
System.out.println("Test " + (passed ? "PASSED" : "FAILED"));
```

### Example 3: Batch Testing Multiple States
```java
List<String> states = Arrays.asList("Alabama", "Alaska", "Arizona", "California");
validator.validateMultipleStates("LLC", "Entity Formation", states);
```

### Example 4: Custom Expected Products
```java
List<String> customProducts = Arrays.asList(
    "Basic LLC Formation",
    "Registered Agent Service",
    "EIN Service"
);

ProductComparisonResult result = validator.validateProducts(
    "LLC", "Alaska", "Entity Formation", null, customProducts
);
```

## 🔧 Configuration

### UI Locators (ProductFilterPage.java)
Update these locators to match your application:

```java
// Dropdown locators
private final By categoryDropdown = By.xpath("//label[contains(text(),'Category')]/following::div[contains(@class,'p-dropdown')][1]");
private final By entityTypeDropdown = By.xpath("//label[contains(text(),'Entity Type')]/following::div[contains(@class,'p-dropdown')][1]");

// Product list locators
private final By productList = By.xpath("//div[contains(@class,'product-list')]");
private final By productItems = By.xpath(".//div[contains(@class,'product-item')]");
```

### Excel File Paths
Update paths in `ProductDataReader.java` if needed:

```java
String filePath = "src/resources/testdata/" + fileName;
```

## 📝 Result Output

### Console Output
```
=== PRODUCT COMPARISON RESULT ===
Entity Type: LLC
State: Alaska
Category: Entity Formation

Expected Products (4): Basic LLC Formation, Expedited LLC Formation, Registered Agent Service, EIN Service
Actual Products (3): Basic LLC Formation, Registered Agent Service, EIN Service

✅ MATCHED (3): Basic LLC Formation, Registered Agent Service, EIN Service
❌ MISSING (1): Expedited LLC Formation

Test Result: FAILED ❌
================================
```

### Log Files
Results are automatically saved to:
- `test-results/product_validation_[timestamp].log`

## 🧪 Running Tests

### Using TestNG
```java
@Test
public void testProductValidation() {
    ProductValidator validator = new ProductValidator();
    ProductComparisonResult result = validator.validateProductsForCategory(
        "LLC", "Alaska", "Entity Formation", null
    );
    Assert.assertTrue(result.isTestPassed(), "Product validation failed");
}
```

### Using Main Method
```java
public static void main(String[] args) {
    DriverManager.initDriver();
    ProductValidator validator = new ProductValidator();
    validator.dynamicOrder("Alaska", "LLC");
    DriverManager.quitDriver();
}
```

## 🔍 Troubleshooting

### Common Issues:

1. **Excel File Not Found**
   - Ensure LLC.xlsx/Corp.xlsx exist in `src/resources/testdata/`
   - Run ExcelFileCreator to create sample files

2. **Sheet Not Found**
   - Check sheet names match category names exactly
   - Use ProductDataReader.getAvailableCategories() to list sheets

3. **State Not Found**
   - Verify state name spelling in Excel headers
   - Use ProductDataReader.getAvailableStates() to list available states

4. **No Products Captured from UI**
   - Update locators in ProductFilterPage.java
   - Check if filters are applied correctly
   - Verify page load timing

5. **Login Issues**
   - Update login steps in ProductValidationTest.setUp()
   - Ensure FieldInputSheet.xlsx has correct login data

### Debug Mode:
Enable detailed logging by adding:
```java
System.setProperty("webdriver.chrome.verboseLogging", "true");
```

## 🚀 Advanced Features

### Custom Product Locators
Add application-specific product locators in ProductFilterPage:

```java
private final By customProductItems = By.xpath("//your-custom-xpath");
```

### County Filtering
Enable county filtering by updating the UI locators:

```java
private final By countyDropdown = By.xpath("//label[contains(text(),'County')]/following::div[contains(@class,'p-dropdown')][1]");
```

### Result Export
Extend writeResultsToExcel() method to create detailed Excel reports.

## 📞 Support

For issues or questions:
1. Check the console output for detailed error messages
2. Review the generated log files in `test-results/`
3. Verify Excel file structure matches the expected format
4. Update UI locators if the application structure changes

---

**Note**: This framework is designed to be generic and can be adapted for any application by updating the UI locators and Excel file structure.
