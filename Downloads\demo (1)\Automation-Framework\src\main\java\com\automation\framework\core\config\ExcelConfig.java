package com.automation.framework.core.config;

/**
 * Excel column configuration for the automation framework
 * Centralized configuration for all Excel-related column mappings
 */
public class ExcelConfig {
    
    // Excel file settings
    public static final String EXCEL_FILE_PATH = "src/resources/testdata/FieldInputSheet.xlsx";
    public static final String SHEET_NAME = "Sheet1";

    // Basic test case columns
    public static final int TEST_CASE_ID_COLUMN = 1;        // Column A: TC001, TC002, etc.
    public static final int TEST_CASE_NAME_COLUMN = 2;      // Column B: Test case name
    public static final int STEP_DESCRIPTION_COLUMN = 6;    // Column F: Step description
    public static final int TEST_DATA_COLUMN = 9;           // Column I: Test data
    public static final int XPATH_COLUMN = 13;              // Column M: XPath

    // Database validation columns
    public static final int DB_TABLE_COLUMN = 14;           // Column N: Database table name
    public static final int DB_COLUMNS_COLUMN = 15;         // Column O: Database columns to check
    public static final int DB_WHERE_CONDITION_COLUMN = 16; // Column P: WHERE condition
    public static final int DB_EXPECTED_VALUES_COLUMN = 17; // Column Q: Expected values
    public static final int DB_UNIQUE_IDENTIFIER_COLUMN = 18; // Column R: Unique identifier strategy
    public static final int DB_TIMESTAMP_COLUMN = 19;       // Column S: Timestamp column name
    public static final int DB_ADDITIONAL_FILTERS_COLUMN = 20; // Column T: Additional filters

    // UI validation columns (future expansion)
    public static final int UI_VALIDATION_TYPE_COLUMN = 21; // Column U: UI validation type
    public static final int UI_EXPECTED_VALUE_COLUMN = 22;  // Column V: UI expected value
    public static final int UI_VALIDATION_XPATH_COLUMN = 23; // Column W: UI validation XPath

    // API validation columns (future expansion)
    public static final int API_ENDPOINT_COLUMN = 24;       // Column X: API endpoint
    public static final int API_METHOD_COLUMN = 25;         // Column Y: HTTP method
    public static final int API_EXPECTED_RESPONSE_COLUMN = 26; // Column Z: Expected response

    // Alternative sheet names to try if default doesn't work
    public static final String[] POSSIBLE_SHEET_NAMES = {
        "Sheet1", "Sheet 1", "TestCases", "Test Cases", "Data", "Main"
    };

    /**
     * Print current Excel configuration
     */
    public static void printConfiguration() {
        System.out.println("📋 Excel Configuration:");
        System.out.println("   File: " + EXCEL_FILE_PATH);
        System.out.println("   Sheet: " + SHEET_NAME);
        System.out.println("   📊 Basic Columns:");
        System.out.println("      Test Case ID: " + TEST_CASE_ID_COLUMN + " (" + getColumnLetter(TEST_CASE_ID_COLUMN) + ")");
        System.out.println("      Step Description: " + STEP_DESCRIPTION_COLUMN + " (" + getColumnLetter(STEP_DESCRIPTION_COLUMN) + ")");
        System.out.println("      Test Data: " + TEST_DATA_COLUMN + " (" + getColumnLetter(TEST_DATA_COLUMN) + ")");
        System.out.println("      XPath: " + XPATH_COLUMN + " (" + getColumnLetter(XPATH_COLUMN) + ")");
        System.out.println("   🗄️ Database Validation Columns:");
        System.out.println("      DB Table: " + DB_TABLE_COLUMN + " (" + getColumnLetter(DB_TABLE_COLUMN) + ")");
        System.out.println("      DB Columns: " + DB_COLUMNS_COLUMN + " (" + getColumnLetter(DB_COLUMNS_COLUMN) + ")");
        System.out.println("      DB Where Condition: " + DB_WHERE_CONDITION_COLUMN + " (" + getColumnLetter(DB_WHERE_CONDITION_COLUMN) + ")");
        System.out.println("      DB Expected Values: " + DB_EXPECTED_VALUES_COLUMN + " (" + getColumnLetter(DB_EXPECTED_VALUES_COLUMN) + ")");
        System.out.println("      DB Unique Identifier: " + DB_UNIQUE_IDENTIFIER_COLUMN + " (" + getColumnLetter(DB_UNIQUE_IDENTIFIER_COLUMN) + ")");
        System.out.println("      DB Timestamp Column: " + DB_TIMESTAMP_COLUMN + " (" + getColumnLetter(DB_TIMESTAMP_COLUMN) + ")");
        System.out.println("      DB Additional Filters: " + DB_ADDITIONAL_FILTERS_COLUMN + " (" + getColumnLetter(DB_ADDITIONAL_FILTERS_COLUMN) + ")");
    }

    /**
     * Convert column number to letter (1=A, 2=B, etc.)
     */
    public static String getColumnLetter(int columnNumber) {
        StringBuilder result = new StringBuilder();
        while (columnNumber > 0) {
            columnNumber--;
            result.insert(0, (char) ('A' + columnNumber % 26));
            columnNumber /= 26;
        }
        return result.toString();
    }

    /**
     * Get column number from letter (A=1, B=2, etc.)
     */
    public static int getColumnNumber(String columnLetter) {
        int result = 0;
        for (int i = 0; i < columnLetter.length(); i++) {
            result = result * 26 + (columnLetter.charAt(i) - 'A' + 1);
        }
        return result;
    }

    /**
     * Validate if a column number is within valid range
     */
    public static boolean isValidColumn(int columnNumber) {
        return columnNumber > 0 && columnNumber <= 16384; // Excel max columns
    }

    /**
     * Get all database validation columns as array
     */
    public static int[] getDatabaseValidationColumns() {
        return new int[] {
            DB_TABLE_COLUMN,
            DB_COLUMNS_COLUMN,
            DB_WHERE_CONDITION_COLUMN,
            DB_EXPECTED_VALUES_COLUMN,
            DB_UNIQUE_IDENTIFIER_COLUMN,
            DB_TIMESTAMP_COLUMN,
            DB_ADDITIONAL_FILTERS_COLUMN
        };
    }

    /**
     * Get all basic test case columns as array
     */
    public static int[] getBasicTestCaseColumns() {
        return new int[] {
            TEST_CASE_ID_COLUMN,
            TEST_CASE_NAME_COLUMN,
            STEP_DESCRIPTION_COLUMN,
            TEST_DATA_COLUMN,
            XPATH_COLUMN
        };
    }
}
