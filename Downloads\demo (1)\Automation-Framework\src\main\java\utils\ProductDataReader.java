package utils;

import models.TestData;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class to read product data from Excel files (LLC.xlsx, Corp.xlsx)
 */
public class ProductDataReader {
    private static final String userDirectory = System.getProperty("user.dir");

    /**
     * Reads expected product data from Excel file based on entity type, state, and category
     * 
     * @param entityType LLC or Corp
     * @param state State name
     * @param category Category name (sheet name)
     * @return TestData object with expected products
     */
    public static TestData readExpectedProducts(String entityType, String state, String category) {
        String fileName = entityType.toUpperCase() + ".xlsx";
        String filePath = "src/resources/testdata/" + fileName;
        
        TestData testData = new TestData(entityType, state, category);
        
        try {
            List<String> products = readProductsFromExcel(filePath, category, state);
            testData.setExpectedProducts(products);
            
            System.out.println("✅ Successfully read " + products.size() + " expected products for " + 
                             entityType + " - " + state + " - " + category);
            
        } catch (Exception e) {
            System.err.println("❌ Error reading expected products: " + e.getMessage());
            e.printStackTrace();
        }
        
        return testData;
    }

    /**
     * Reads products from Excel file for a specific sheet and state
     * 
     * @param filePath Path to Excel file
     * @param sheetName Sheet name (category)
     * @param stateName State to look for
     * @return List of product names
     */
    private static List<String> readProductsFromExcel(String filePath, String sheetName, String stateName) 
            throws IOException {
        List<String> products = new ArrayList<>();
        String fullPath = userDirectory + "/" + filePath;
        
        try (FileInputStream inputStream = new FileInputStream(fullPath);
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new RuntimeException("Sheet '" + sheetName + "' not found in " + filePath);
            }
            
            // Find the header row and state column
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("Header row not found in sheet: " + sheetName);
            }
            
            int stateColumnIndex = findStateColumn(headerRow, stateName);
            if (stateColumnIndex == -1) {
                throw new RuntimeException("State '" + stateName + "' not found in sheet: " + sheetName);
            }
            
            // Read products from the state column
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell cell = row.getCell(stateColumnIndex);
                    if (cell != null) {
                        String cellValue = getCellValueAsString(cell);
                        if (!cellValue.isEmpty()) {
                            // Handle comma-separated products in a single cell
                            String[] productArray = cellValue.split(",");
                            for (String product : productArray) {
                                String trimmedProduct = product.trim();
                                if (!trimmedProduct.isEmpty()) {
                                    products.add(trimmedProduct);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return products;
    }

    /**
     * Finds the column index for a specific state in the header row
     */
    private static int findStateColumn(Row headerRow, String stateName) {
        for (int colIndex = 0; colIndex < headerRow.getLastCellNum(); colIndex++) {
            Cell cell = headerRow.getCell(colIndex);
            if (cell != null) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue.equalsIgnoreCase(stateName)) {
                    return colIndex;
                }
            }
        }
        return -1;
    }

    /**
     * Converts cell value to string regardless of cell type
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // Format numeric values to avoid scientific notation
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
            default:
                return "";
        }
    }

    /**
     * Gets all available sheets (categories) from an Excel file
     */
    public static List<String> getAvailableCategories(String entityType) {
        List<String> categories = new ArrayList<>();
        String fileName = entityType.toUpperCase() + ".xlsx";
        String filePath = "src/resources/testdata/" + fileName;
        String fullPath = userDirectory + "/" + filePath;
        
        try (FileInputStream inputStream = new FileInputStream(fullPath);
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                categories.add(workbook.getSheetName(i));
            }
            
        } catch (IOException e) {
            System.err.println("❌ Error reading categories from " + fileName + ": " + e.getMessage());
        }
        
        return categories;
    }

    /**
     * Gets all available states from a specific category sheet
     */
    public static List<String> getAvailableStates(String entityType, String category) {
        List<String> states = new ArrayList<>();
        String fileName = entityType.toUpperCase() + ".xlsx";
        String filePath = "src/resources/testdata/" + fileName;
        String fullPath = userDirectory + "/" + filePath;
        
        try (FileInputStream inputStream = new FileInputStream(fullPath);
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheet(category);
            if (sheet != null) {
                Row headerRow = sheet.getRow(0);
                if (headerRow != null) {
                    for (int colIndex = 1; colIndex < headerRow.getLastCellNum(); colIndex++) {
                        Cell cell = headerRow.getCell(colIndex);
                        if (cell != null) {
                            String stateName = getCellValueAsString(cell);
                            if (!stateName.isEmpty()) {
                                states.add(stateName);
                            }
                        }
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("❌ Error reading states from " + fileName + ": " + e.getMessage());
        }
        
        return states;
    }
}
