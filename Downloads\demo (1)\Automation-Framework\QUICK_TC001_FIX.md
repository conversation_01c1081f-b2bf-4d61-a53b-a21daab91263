# 🔧 Quick TC001 Fix Guide

## 🎯 **Most Likely Issues & Solutions**

### **Issue 1: Column Index Problem**

Your Excel structure might be different. Try updating the column indices in `TestCaseExecutor.java`:

```java
// Current settings (line 23-27 in TestCaseExecutor.java)
private static final int TEST_CASE_ID_COLUMN = 1;  // Column A
private static final int STEP_DESCRIPTION_COLUMN = 6; // Column F  
private static final int TEST_DATA_COLUMN = 9;     // Column I
private static final int XPATH_COLUMN = 13;        // Column M

// If TC001 is in a different column, try:
private static final int TEST_CASE_ID_COLUMN = 2;  // Column B
// or
private static final int TEST_CASE_ID_COLUMN = 3;  // Column C
```

### **Issue 2: Sheet Name Problem**

Update line 34 in `TestCaseExecutor.java`:

```java
// Current setting
this.sheetName = detectCorrectSheetName();

// If your sheet has a different name, try:
this.sheetName = "Sheet 1";  // With space
// or
this.sheetName = "TestCases";
// or check your actual sheet name in Excel
```

### **Issue 3: Excel Structure Verification**

Open your `FieldInputSheet.xlsx` and verify:

1. **TC001 Location**: Which column contains "TC001"?
   - Column A (index 1) ✅ Current setting
   - Column B (index 2) → Update TEST_CASE_ID_COLUMN = 2
   - Column C (index 3) → Update TEST_CASE_ID_COLUMN = 3

2. **XPath Location**: Which column contains your XPaths?
   - Column M (index 13) ✅ Current setting  
   - Column N (index 14) → Update XPATH_COLUMN = 14
   - Different column → Update accordingly

3. **Sheet Name**: What's the exact sheet name?
   - "Sheet1" ✅ Current setting
   - "Sheet 1" → Update sheetName = "Sheet 1"
   - Other name → Update accordingly

## 🚀 **Quick Fix Steps**

### **Step 1: Check Your Excel Structure**

1. Open `src/resources/testdata/FieldInputSheet.xlsx`
2. Note which column has "TC001"
3. Note which column has your XPaths
4. Note the exact sheet name

### **Step 2: Update TestCaseExecutor.java**

Based on your Excel structure, update these lines in `src/main/java/utils/TestCaseExecutor.java`:

```java
// Lines 23-27: Update column indices
private static final int TEST_CASE_ID_COLUMN = X;  // Your TC001 column number
private static final int XPATH_COLUMN = Y;         // Your XPath column number

// Line 34: Update sheet name if needed
this.sheetName = "YourActualSheetName";
```

### **Step 3: Test the Fix**

Run your test again:

```java
TestCaseExecutor executor = new TestCaseExecutor();
boolean result = executor.executeTestCase("TC001");
```

## 📊 **Expected Excel Structure**

Based on your description, your Excel should look like:

| A | B | C | D | E | F | G | H | I | J | K | L | M |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| TC001 | Sign-In Page | ... | ... | ... | 1. Enter Email | ... | ... | <EMAIL> | ... | ... | ... | //*[@id="email"] |
| TC001 | Sign-In Page | ... | ... | ... | 2. Enter Password | ... | ... | Snehal@123 | ... | ... | ... | //*[@id="password"]/div/input |
| TC001 | Sign-In Page | ... | ... | ... | 3. Submit Button | ... | ... |  | ... | ... | ... | //*[@id="root"]/div/div/div[2]/div[2]/form/button |

**Column Mapping:**
- **Column A (1)**: TC001 ← TEST_CASE_ID_COLUMN
- **Column F (6)**: Step Description ← STEP_DESCRIPTION_COLUMN  
- **Column I (9)**: Test Data ← TEST_DATA_COLUMN
- **Column M (13)**: XPath ← XPATH_COLUMN

## 🔧 **Alternative: Manual Column Detection**

If you're unsure about column numbers, temporarily add this debug code to `TestCaseExecutor.java`:

```java
// Add this method to TestCaseExecutor class
public void debugExcelStructure() {
    try {
        System.out.println("🔍 Excel Structure Debug:");
        for (int row = 1; row <= 5; row++) {
            System.out.println("Row " + row + ":");
            for (int col = 1; col <= 15; col++) {
                String value = excelUtils.getCellData(excelFilePath, sheetName, row, col);
                System.out.println("  Col " + col + " (" + getColumnLetter(col) + "): '" + value + "'");
            }
        }
    } catch (Exception e) {
        System.err.println("Debug error: " + e.getMessage());
    }
}

// Call this method before executing TC001
executor.debugExcelStructure();
```

## ✅ **Verification Checklist**

- [ ] TC001 is in the correct column (update TEST_CASE_ID_COLUMN)
- [ ] XPaths are in the correct column (update XPATH_COLUMN)  
- [ ] Sheet name is correct (update sheetName)
- [ ] Excel file exists at `src/resources/testdata/FieldInputSheet.xlsx`
- [ ] TC001 rows have non-empty XPath values

## 🎯 **Most Common Fix**

**90% of TC001 issues are solved by:**

1. **Updating TEST_CASE_ID_COLUMN** to the correct column number where TC001 is located
2. **Updating the sheet name** to match your Excel exactly

Try these first! 🚀
