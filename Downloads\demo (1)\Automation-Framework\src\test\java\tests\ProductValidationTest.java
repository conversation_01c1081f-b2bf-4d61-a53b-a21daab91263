package tests;

import drivers.DriverManager;
import models.ProductComparisonResult;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.annotations.DataProvider;
import pages.ProductValidator;
import utiles.SmartActionUtils;

/**
 * Test class for Product Validation functionality
 */
public class ProductValidationTest {

    private ProductValidator validator;
    private SmartActionUtils utils;

    @BeforeClass
    public void setUp() {
        System.out.println("🚀 Setting up Product Validation Test");

        // Initialize WebDriver
        DriverManager.initDriver();

        // Initialize utility classes
        utils = new SmartActionUtils(DriverManager.getDriver());
        validator = new ProductValidator();

        // Perform login if needed
        try {
            performLogin();
        } catch (Exception e) {
            System.err.println("⚠️ Login failed or not needed: " + e.getMessage());
        }
    }

    private void performLogin() throws InterruptedException {
        // Login steps using existing SmartActionUtils
        // These row numbers should match your FieldInputSheet.xlsx
        utils.performSmartActionWithRetry(5); // email
        utils.performSmartActionWithRetry(6); // password
        utils.performSmartActionWithRetry(7); // login button

        // Additional login step if needed
        try {
            utils.performSmartActionWithRetry(11);
        } catch (Exception e) {
            System.out.println("⚠️ Additional login step not needed: " + e.getMessage());
        }

        Thread.sleep(3000); // Wait for login to complete
        System.out.println("✅ Login completed");
    }

    @Test(dataProvider = "testData")
    public void testProductValidation(String entityType, String state, String category) {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🧪 Testing: " + entityType + " - " + state + " - " + category);
        System.out.println("=".repeat(60));

        ProductComparisonResult result = validator.validateProductsForCategory(entityType, state, category, null);

        // Log detailed results
        System.out.println(result.getResultSummary());

        // Assert that the test passed
        if (!result.isTestPassed()) {
            System.err.println("❌ Test failed for: " + entityType + " - " + state + " - " + category);
            System.err.println("Missing products: " + result.getMissingProducts());
            System.err.println("Extra products: " + result.getExtraProducts());
        }

        // For demonstration purposes, we'll make this a soft assertion
        // In a real test, you might want to use Assert.assertTrue(result.isTestPassed())
        System.out.println(result.isTestPassed() ? "✅ PASSED" : "❌ FAILED");
    }

    @Test
    public void testLLCAlaskaEntityFormation() {
        System.out.println("\n🧪 Testing specific case: LLC - Alaska - Entity Formation");

        ProductComparisonResult result = validator.validateProductsForCategory("LLC", "Alaska", "Entity Formation", null);

        System.out.println(result.getResultSummary());

        // Verify that we got some results
        Assert.assertNotNull(result.getActualProducts(), "Actual products should not be null");
        Assert.assertNotNull(result.getExpectedProducts(), "Expected products should not be null");

        System.out.println("✅ Test completed - check logs for detailed results");
    }

    @Test
    public void testCorpCaliforniaEntityFormation() {
        System.out.println("\n🧪 Testing specific case: Corp - California - Entity Formation");

        ProductComparisonResult result = validator.validateProductsForCategory("Corp", "California", "Entity Formation", null);

        System.out.println(result.getResultSummary());

        // Verify that we got some results
        Assert.assertNotNull(result.getActualProducts(), "Actual products should not be null");
        Assert.assertNotNull(result.getExpectedProducts(), "Expected products should not be null");

        System.out.println("✅ Test completed - check logs for detailed results");
    }

    @Test
    public void testQuickValidation() {
        System.out.println("\n🧪 Testing quick validation method");

        boolean result = validator.quickValidate("LLC", "Alaska", "Entity Formation");

        System.out.println("Quick validation result: " + (result ? "PASSED ✅" : "FAILED ❌"));
    }

    @DataProvider(name = "testData")
    public Object[][] getTestData() {
        return new Object[][] {
            {"LLC", "Alaska", "Entity Formation"},
            {"LLC", "Alabama", "Entity Formation"},
            {"Corp", "California", "Entity Formation"},
            {"Corp", "Texas", "Entity Formation"}
            // Add more test combinations as needed
        };
    }

    @Test
    public void testDynamicOrderMethod() {
        System.out.println("\n🧪 Testing dynamic order method (main validation flow)");

        // This will test all categories for LLC in Alaska
        validator.dynamicOrder("Alaska", "LLC");

        System.out.println("✅ Dynamic order test completed");
    }

    @AfterClass
    public void tearDown() {
        System.out.println("\n🏁 Tearing down Product Validation Test");

        // Close the browser
        if (DriverManager.getDriver() != null) {
            DriverManager.quitDriver();
        }

        System.out.println("✅ Test cleanup completed");
    }
}
