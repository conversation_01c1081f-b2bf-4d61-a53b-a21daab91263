# 🎯 Product Card Configuration Guide

This guide shows you exactly how to configure the automation framework for your specific product card structure.

## 🏗️ Your Product Structure

Based on the HTML you provided, your products have this structure:

```html
<div class="product-card">
  <div class="product-content">
    <div class="product-name">ARTICLES OF ORGANIZATION</div>
    <div class="product-description">Our team of experts will prepare...</div>
    <div class="product-price">$99.99</div>
  </div>
  <div class="product-footer">
    <button>Know More</button>
    <button>Add To Cart</button>
  </div>
</div>
```

## ✅ What's Already Optimized

The framework has been **automatically optimized** for your structure:

1. **Product Card Detection**: Looks for `div[@class='product-card']` elements first
2. **Product Name Extraction**: Extracts text from `div[@class='product-name']` 
3. **Smart Filtering**: Automatically filters out prices, buttons, and descriptions
4. **Multiple Fallbacks**: Uses alternative patterns if the primary method fails

## 🔧 What You Need to Configure

### 1. **Dropdown XPaths** (Required)

You need to provide the XPaths for your dropdown elements. Update these in your test:

```java
ProductFilterPage filterPage = new ProductFilterPage(driver);

// Replace these with your actual dropdown XPaths
filterPage.setCustomXPath("category", "YOUR_CATEGORY_DROPDOWN_XPATH");
filterPage.setCustomXPath("entityType", "YOUR_ENTITY_TYPE_DROPDOWN_XPATH");
filterPage.setCustomXPath("state", "YOUR_STATE_DROPDOWN_XPATH");
```

### 2. **Common Dropdown Patterns**

Here are common XPath patterns you might need:

```xpath
# Standard select dropdowns
//select[@id='category']
//select[@name='entity_type']
//select[@class='state-selector']

# PrimeNG dropdowns (common in Angular)
//p-dropdown[@id='category']//div[contains(@class,'p-dropdown')]
//p-dropdown[@formcontrolname='entityType']//div[contains(@class,'p-dropdown')]

# Custom div/button dropdowns
//div[@data-testid='category-select']//button
//div[@class='dropdown-container']//div[@class='trigger']

# React/Vue component dropdowns
//div[@data-cy='category-dropdown']
//div[contains(@class,'category-select')]//button
```

## 🚀 Quick Setup

### Step 1: Find Your Dropdown XPaths

1. **Open your application** in browser
2. **Right-click on each dropdown** → Inspect Element
3. **Copy the XPath** or create one based on the element attributes

### Step 2: Configure the Framework

```java
// In your test class or setup method
ProductFilterPage filterPage = new ProductFilterPage(driver);

// Set your actual XPaths here
filterPage.setCustomXPath("category", "//your-category-xpath");
filterPage.setCustomXPath("entityType", "//your-entity-xpath");  
filterPage.setCustomXPath("state", "//your-state-xpath");
```

### Step 3: Run the Test

```java
// The framework will now:
// 1. Use your custom dropdown XPaths
// 2. Automatically detect your product-card structure
// 3. Extract product names correctly

ProductValidator validator = new ProductValidator();
ProductComparisonResult result = validator.validateProductsForCategory(
    "LLC", "Alaska", "Entity Formation", null
);
```

## 📊 Expected Output

With your product structure, you should see output like:

```
🔄 Applying filters: Category=Entity Formation, EntityType=LLC, State=Alaska
✅ Found 5 product cards using direct search
✅ Method 1: Extracted 5 product names from cards
✅ Captured 5 products from UI: [ARTICLES OF ORGANIZATION, EXPEDITED LLC FORMATION, REGISTERED AGENT SERVICE, EIN SERVICE, OPERATING AGREEMENT]

=== PRODUCT COMPARISON RESULT ===
Entity Type: LLC
State: Alaska  
Category: Entity Formation

Expected Products (4): Basic LLC Formation, Expedited LLC Formation, Registered Agent Service, EIN Service
Actual Products (5): ARTICLES OF ORGANIZATION, EXPEDITED LLC FORMATION, REGISTERED AGENT SERVICE, EIN SERVICE, OPERATING AGREEMENT

✅ MATCHED (3): EXPEDITED LLC FORMATION, REGISTERED AGENT SERVICE, EIN SERVICE
❌ MISSING (1): Basic LLC Formation
❌ EXTRA (2): ARTICLES OF ORGANIZATION, OPERATING AGREEMENT
```

## 🔍 Troubleshooting

### Issue 1: No Products Found
```
⚠️ No products found with standard locators, trying alternative methods...
```

**Solution**: The product cards might not be visible yet. Add a wait or check if filters are applied correctly.

### Issue 2: Wrong Product Names
```
✅ Captured 5 products: [$99.99, Know More, Add To Cart, Our team of experts, ARTICLES OF ORGANIZATION]
```

**Solution**: The smart filtering should prevent this, but if it happens, use the exact XPath:

```java
String exactXPath = "//div[@class='product-card']//div[@class='product-name']";
List<String> products = filterPage.captureProductsWithCustomXPath(exactXPath);
```

### Issue 3: Dropdown Selection Failed
```
❌ Failed to select 'LLC' from Entity Type dropdown
```

**Solution**: Check your dropdown XPath and make sure the dropdown opens correctly:

```java
// Test your XPath in browser console:
$x("//your-dropdown-xpath")  // Should return the dropdown element
```

## 🎯 Advanced Configuration

### Store XPaths in Excel

Create a configuration sheet in your Excel file:

| Element | XPath | Description |
|---------|-------|-------------|
| category | //select[@id='category'] | Category dropdown |
| entityType | //p-dropdown[@id='entity']//div | Entity type selector |
| state | //div[@data-test='state']//button | State dropdown |
| products | //div[@class='product-card']//div[@class='product-name'] | Product names |

Then read them dynamically:

```java
ExcelUtils excelUtils = new ExcelUtils();
String categoryXPath = excelUtils.getCellData("config.xlsx", "XPaths", 1, 2);
String stateXPath = excelUtils.getCellData("config.xlsx", "XPaths", 3, 2);

filterPage.setCustomXPath("category", categoryXPath);
filterPage.setCustomXPath("state", stateXPath);
```

### Environment-Specific Configuration

```java
// Different XPaths for different environments
String environment = System.getProperty("test.environment", "staging");

if ("production".equals(environment)) {
    filterPage.setCustomXPath("category", "//select[@id='prod-category']");
} else {
    filterPage.setCustomXPath("category", "//select[@id='staging-category']");
}
```

## ✅ Verification Checklist

- [ ] Dropdown XPaths are configured and working
- [ ] Product cards are detected correctly  
- [ ] Product names are extracted (not prices/buttons)
- [ ] Filters apply successfully
- [ ] Products appear after filter application
- [ ] Comparison logic works with your product names

---

**🎉 Your framework is now optimized for your product-card structure! Just configure the dropdown XPaths and you're ready to go!**
