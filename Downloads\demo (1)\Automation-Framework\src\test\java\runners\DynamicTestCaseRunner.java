package runners;

import drivers.DriverManager;
import utils.TestCaseExecutor;

import java.util.List;
import java.util.Scanner;

/**
 * Dynamic Test Case Runner - Execute any test case by ID
 * This allows you to run any test case (TC001, TC002, etc.) dynamically
 */
public class DynamicTestCaseRunner {

    public static void main(String[] args) {
        System.out.println("🚀 Dynamic Test Case Runner");
        System.out.println("=" + "=".repeat(40));

        TestCaseExecutor testExecutor = null;
        
        try {
            // Initialize WebDriver
            DriverManager.initDriver();
            
            // Initialize test executor
            testExecutor = new TestCaseExecutor();
            
            if (args.length > 0) {
                // Run test cases provided as command line arguments
                runTestCasesFromArgs(testExecutor, args);
            } else {
                // Interactive mode
                runInteractiveMode(testExecutor);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
        }
    }

    /**
     * Run test cases provided as command line arguments
     * Usage: java DynamicTestCaseRunner TC001 TC002 TC003
     */
    private static void runTestCasesFromArgs(TestCaseExecutor testExecutor, String[] testCaseIds) {
        System.out.println("📋 Running test cases from command line arguments:");
        
        for (String testCaseId : testCaseIds) {
            System.out.println("  - " + testCaseId);
        }
        
        System.out.println();
        
        boolean allPassed = testExecutor.executeMultipleTestCases(testCaseIds);
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("📊 EXECUTION SUMMARY");
        System.out.println("=".repeat(50));
        System.out.println("Total Test Cases: " + testCaseIds.length);
        System.out.println("Result: " + (allPassed ? "ALL PASSED ✅" : "SOME FAILED ❌"));
        System.out.println("=".repeat(50));
    }

    /**
     * Interactive mode - allows user to select test cases to run
     */
    private static void runInteractiveMode(TestCaseExecutor testExecutor) {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            try {
                System.out.println("\n🎯 DYNAMIC TEST CASE EXECUTION");
                System.out.println("-".repeat(40));
                
                // Show available test cases
                List<String> availableTestCases = testExecutor.getAvailableTestCases();
                if (availableTestCases.isEmpty()) {
                    System.out.println("❌ No test cases found in Excel file");
                    break;
                }
                
                System.out.println("📋 Available Test Cases:");
                for (int i = 0; i < availableTestCases.size(); i++) {
                    System.out.println("  " + (i + 1) + ". " + availableTestCases.get(i));
                }
                
                System.out.println("\n🔧 Options:");
                System.out.println("  • Enter test case ID (e.g., TC001)");
                System.out.println("  • Enter multiple IDs separated by space (e.g., TC001 TC002)");
                System.out.println("  • Type 'all' to run all test cases");
                System.out.println("  • Type 'exit' to quit");
                
                System.out.print("\n👉 Your choice: ");
                String input = scanner.nextLine().trim();
                
                if (input.equalsIgnoreCase("exit")) {
                    System.out.println("👋 Goodbye!");
                    break;
                }
                
                if (input.equalsIgnoreCase("all")) {
                    System.out.println("\n🚀 Running ALL test cases...");
                    boolean allPassed = testExecutor.executeMultipleTestCases(
                        availableTestCases.toArray(new String[0])
                    );
                    System.out.println("\n📊 All test cases result: " + 
                        (allPassed ? "PASSED ✅" : "FAILED ❌"));
                    continue;
                }
                
                // Parse input for test case IDs
                String[] testCaseIds = input.split("\\s+");
                boolean validInput = true;
                
                for (String testCaseId : testCaseIds) {
                    if (!availableTestCases.contains(testCaseId.toUpperCase())) {
                        System.out.println("❌ Invalid test case ID: " + testCaseId);
                        validInput = false;
                    }
                }
                
                if (!validInput) {
                    continue;
                }
                
                // Execute the test cases
                System.out.println("\n🚀 Executing test case(s): " + String.join(", ", testCaseIds));
                
                if (testCaseIds.length == 1) {
                    boolean result = testExecutor.executeTestCase(testCaseIds[0].toUpperCase());
                    System.out.println("\n📊 Result: " + (result ? "PASSED ✅" : "FAILED ❌"));
                } else {
                    // Convert to uppercase
                    for (int i = 0; i < testCaseIds.length; i++) {
                        testCaseIds[i] = testCaseIds[i].toUpperCase();
                    }
                    boolean allPassed = testExecutor.executeMultipleTestCases(testCaseIds);
                    System.out.println("\n📊 Overall result: " + (allPassed ? "ALL PASSED ✅" : "SOME FAILED ❌"));
                }
                
            } catch (Exception e) {
                System.err.println("❌ Error in interactive mode: " + e.getMessage());
            }
        }
        
        scanner.close();
    }
}

/**
 * USAGE EXAMPLES:
 * 
 * 1. Interactive Mode:
 *    java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner
 * 
 * 2. Command Line Mode:
 *    java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner TC001
 *    java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner TC001 TC002 TC003
 * 
 * 3. Maven Execution:
 *    mvn exec:java -Dexec.mainClass="runners.DynamicTestCaseRunner" -Dexec.args="TC001"
 */
