package utils;

import config.ExcelConfig;
import database.DatabaseManager;
import database.DatabaseValidator;
import models.DatabaseTestData;
import models.DatabaseValidationResult;
import utiles.ExcelUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Database Validation Executor that reads database validation parameters from Excel
 * and performs database validations for test cases
 */
public class DatabaseValidationExecutor {
    private ExcelUtils excelUtils;
    private String excelFilePath;
    private String sheetName;

    public DatabaseValidationExecutor() {
        this.excelUtils = new ExcelUtils();
        this.excelFilePath = ExcelConfig.EXCEL_FILE_PATH;
        this.sheetName = ExcelConfig.SHEET_NAME;

        System.out.println("🔍 DatabaseValidationExecutor initialized:");
        System.out.println("   File: " + excelFilePath);
        System.out.println("   Sheet: " + sheetName);
    }

    /**
     * Execute database validation for a specific test case
     */
    public DatabaseValidationResult executeValidation(String testCaseId) {
        System.out.println("🚀 Starting database validation for test case: " + testCaseId);

        // Initialize database connection
        DatabaseManager.initialize();

        // Test database connectivity first
        if (!DatabaseValidator.testDatabaseConnection()) {
            DatabaseValidationResult result = new DatabaseValidationResult(testCaseId);
            result.addOverallError("Database connection failed - cannot perform validation");
            return result;
        }

        // Read database validation steps from Excel
        List<DatabaseTestData> validationSteps = readDatabaseValidationSteps(testCaseId);

        if (validationSteps.isEmpty()) {
            System.out.println("⚠️ No database validation steps found for test case: " + testCaseId);
            DatabaseValidationResult result = new DatabaseValidationResult(testCaseId);
            result.addOverallError("No database validation steps configured");
            return result;
        }

        // Execute validations
        DatabaseValidationResult result = DatabaseValidator.validateSteps(validationSteps);

        // Print summary
        System.out.println(result.getSummary());

        return result;
    }

    /**
     * Read database validation steps from Excel for a specific test case
     */
    public List<DatabaseTestData> readDatabaseValidationSteps(String testCaseId) {
        List<DatabaseTestData> validationSteps = new ArrayList<>();

        try {
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);
            System.out.println("📊 Reading database validation data from " + totalRows + " rows");

            for (int row = 1; row <= totalRows; row++) {
                // Check if this row belongs to the test case
                String currentTestCaseId = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.TEST_CASE_ID_COLUMN);

                if (testCaseId.equalsIgnoreCase(currentTestCaseId)) {
                    DatabaseTestData stepData = readDatabaseValidationStep(row);
                    if (stepData != null) {
                        validationSteps.add(stepData);
                        System.out.println("   ✅ Added database validation step: " + stepData.getStepDescription());
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error reading database validation steps: " + e.getMessage());
        }

        System.out.println("📋 Found " + validationSteps.size() + " database validation steps for " + testCaseId);
        return validationSteps;
    }

    /**
     * Read database validation data from a specific Excel row
     */
    private DatabaseTestData readDatabaseValidationStep(int row) {
        try {
            // Read basic test data
            String testCaseId = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.TEST_CASE_ID_COLUMN);
            String stepDescription = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.STEP_DESCRIPTION_COLUMN);
            String testData = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.TEST_DATA_COLUMN);

            // Read database validation data
            String tableName = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_TABLE_COLUMN);
            String columnsStr = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_COLUMNS_COLUMN);
            String whereCondition = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_WHERE_CONDITION_COLUMN);
            String expectedValuesStr = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_EXPECTED_VALUES_COLUMN);

            // Read unique identifier configuration
            String uniqueStrategy = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_UNIQUE_IDENTIFIER_COLUMN);
            String timestampColumn = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_TIMESTAMP_COLUMN);
            String additionalFilters = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_ADDITIONAL_FILTERS_COLUMN);

            // Skip if no database validation configured
            if (tableName == null || tableName.trim().isEmpty()) {
                return null;
            }

            // Create DatabaseTestData object
            DatabaseTestData stepData = new DatabaseTestData(testCaseId, stepDescription);
            stepData.setTestData(testData);
            stepData.setTableName(tableName.trim());
            stepData.setWhereCondition(whereCondition != null ? whereCondition.trim() : "");

            // Parse columns to check
            if (columnsStr != null && !columnsStr.trim().isEmpty()) {
                List<String> columns = Arrays.asList(columnsStr.split(","));
                for (String column : columns) {
                    stepData.addColumnToCheck(column.trim());
                }
            }

            // Parse expected values
            if (expectedValuesStr != null && !expectedValuesStr.trim().isEmpty()) {
                List<String> expectedValues = Arrays.asList(expectedValuesStr.split(","));
                for (String value : expectedValues) {
                    stepData.addExpectedValue(value.trim());
                }
            }

            return stepData;

        } catch (Exception e) {
            System.err.println("❌ Error reading database validation step from row " + row + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Get all test cases that have database validation configured
     */
    public List<String> getTestCasesWithDatabaseValidation() {
        List<String> testCases = new ArrayList<>();

        try {
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);

            for (int row = 1; row <= totalRows; row++) {
                String testCaseId = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.TEST_CASE_ID_COLUMN);
                String tableName = excelUtils.getCellData(excelFilePath, sheetName, row, ExcelConfig.DB_TABLE_COLUMN);

                if (testCaseId != null && !testCaseId.trim().isEmpty() &&
                    tableName != null && !tableName.trim().isEmpty() &&
                    !testCases.contains(testCaseId)) {
                    testCases.add(testCaseId);
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error getting test cases with database validation: " + e.getMessage());
        }

        return testCases;
    }

    /**
     * Validate database configuration and Excel structure
     */
    public boolean validateConfiguration() {
        System.out.println("🔍 Validating database validation configuration...");

        boolean isValid = true;
        List<String> issues = new ArrayList<>();

        // Check database connection
        try {
            DatabaseManager.initialize();
            if (!DatabaseValidator.testDatabaseConnection()) {
                issues.add("Database connection failed");
                isValid = false;
            }
        } catch (Exception e) {
            issues.add("Database initialization error: " + e.getMessage());
            isValid = false;
        }

        // Check Excel file structure
        try {
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);
            if (totalRows == 0) {
                issues.add("Excel file is empty or sheet not found");
                isValid = false;
            }
        } catch (Exception e) {
            issues.add("Excel file access error: " + e.getMessage());
            isValid = false;
        }

        // Print validation results
        if (isValid) {
            System.out.println("✅ Database validation configuration is valid");
        } else {
            System.out.println("❌ Database validation configuration issues found:");
            for (String issue : issues) {
                System.out.println("   - " + issue);
            }
        }

        return isValid;
    }

    /**
     * Print database validation configuration help
     */
    public void printConfigurationHelp() {
        System.out.println("📋 Database Validation Excel Configuration:");
        System.out.println("   Column " + ExcelConfig.DB_TABLE_COLUMN + " (" + getColumnLetter(ExcelConfig.DB_TABLE_COLUMN) + "): Database table name");
        System.out.println("   Column " + ExcelConfig.DB_COLUMNS_COLUMN + " (" + getColumnLetter(ExcelConfig.DB_COLUMNS_COLUMN) + "): Columns to check (comma-separated)");
        System.out.println("   Column " + ExcelConfig.DB_WHERE_CONDITION_COLUMN + " (" + getColumnLetter(ExcelConfig.DB_WHERE_CONDITION_COLUMN) + "): WHERE condition");
        System.out.println("   Column " + ExcelConfig.DB_EXPECTED_VALUES_COLUMN + " (" + getColumnLetter(ExcelConfig.DB_EXPECTED_VALUES_COLUMN) + "): Expected values (comma-separated)");
        System.out.println();
        System.out.println("📝 Example Excel row for signup validation:");
        System.out.println("   DB Table: users");
        System.out.println("   DB Columns: email,status,created_at");
        System.out.println("   DB Where: email='{{TEST_DATA}}'");
        System.out.println("   DB Expected: <EMAIL>,active,NOT_NULL");
        System.out.println();
        System.out.println("🔧 Special expected values:");
        System.out.println("   NOT_NULL: Field should not be null");
        System.out.println("   NULL: Field should be null");
        System.out.println("   EMPTY: Field should be empty string");
        System.out.println("   NOT_EMPTY: Field should not be empty");
        System.out.println("   ANY: Any value is acceptable");
        System.out.println("   {{TEST_DATA}}: Will be replaced with actual test data");
    }

    /**
     * Convert column number to letter (1=A, 2=B, etc.)
     */
    private String getColumnLetter(int columnNumber) {
        StringBuilder result = new StringBuilder();
        while (columnNumber > 0) {
            columnNumber--;
            result.insert(0, (char) ('A' + columnNumber % 26));
            columnNumber /= 26;
        }
        return result.toString();
    }

    /**
     * Cleanup resources
     */
    public void cleanup() {
        DatabaseManager.shutdown();
    }
}
