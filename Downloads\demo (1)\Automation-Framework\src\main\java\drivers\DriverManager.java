package drivers;

import org.openqa.selenium.WebDriver;

public class DriverManager {
    private static final ThreadLocal<WebDriver> driverThread = new ThreadLocal<>();

    public static void initDriver() {
        if (driverThread.get() == null) {
            WebDriver driver = DriverFactory.createDriver();
            driver.manage().window().maximize();
            driver.get(ConfigManager.get("baseUrl"));
            driverThread.set(driver);
        }
    }

    public static WebDriver getDriver() {
        return driverThread.get();
    }

    public static void quitDriver() {
        WebDriver driver = driverThread.get();
        if (driver != null) {
            driver.quit();
            driverThread.remove();
        }
    }
}

