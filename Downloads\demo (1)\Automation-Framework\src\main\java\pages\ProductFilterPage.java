package pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.JavascriptExecutor;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * Dynamic Page Object class for handling product filtering and product list operations
 * Supports dynamic XPath configuration for different applications
 */
public class ProductFilterPage {
    private final WebDriver driver;
    private final WebDriverWait wait;

    // Dynamic locator storage
    private Map<String, String> dynamicXPaths;

    // Default fallback locators (can be overridden)
    private static final String DEFAULT_CATEGORY_XPATH = "//label[contains(text(),'Category') or contains(text(),'Select Category')]/following::div[contains(@class,'p-dropdown')][1]";
    private static final String DEFAULT_ENTITY_TYPE_XPATH = "//label[contains(text(),'Select Entity Type') or contains(text(),'Entity Type')]/following::div[contains(@class,'p-dropdown')][1]";
    private static final String DEFAULT_STATE_XPATH = "//label[contains(text(),'Select State') or contains(text(),'State')]/following::div[contains(@class,'p-dropdown')][1]";
    private static final String DEFAULT_COUNTY_XPATH = "//label[contains(text(),'Select County') or contains(text(),'County')]/following::div[contains(@class,'p-dropdown')][1]";

    // Dynamic product search patterns - these will be tried in order
    private static final String[] PRODUCT_CONTAINER_PATTERNS = {
        "//div[contains(@class,'product-list')]",
        "//div[contains(@class,'products')]",
        "//div[contains(@class,'product-grid')]",
        "//div[contains(@class,'product-container')]",
        "//div[contains(@class,'right')]",
        "//div[contains(@class,'content')]",
        "//div[contains(@class,'main')]",
        "//div[contains(@class,'results')]",
        "//body",  // Fallback to search entire page
        "//div[@id='root']",  // Common React root
        "//main"  // Common main content area
    };

    private static final String[] PRODUCT_ITEM_PATTERNS = {
        ".//div[contains(@class,'product-card')]",  // Your specific structure
        ".//div[contains(@class,'product-item')]",
        ".//li[contains(@class,'product')]",
        ".//div[contains(@class,'card')]",
        ".//div[contains(@class,'item')]",
        ".//li",
        ".//div[contains(@class,'product')]"
    };

    // Product name extraction patterns - specific to your HTML structure
    private static final String[] PRODUCT_NAME_PATTERNS = {
        ".//div[@class='product-name']",  // Your exact structure
        ".//div[contains(@class,'product-name')]",
        ".//div[contains(@class,'name')]",
        ".//h1", ".//h2", ".//h3", ".//h4", ".//h5", ".//h6",
        ".//span[contains(@class,'title')]",
        ".//div[contains(@class,'title')]",
        ".//span[contains(@class,'name')]"
    };

    public ProductFilterPage(WebDriver driver) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        this.dynamicXPaths = new HashMap<>();
        initializeDefaultXPaths();
    }

    /**
     * Constructor with custom XPaths
     */
    public ProductFilterPage(WebDriver driver, Map<String, String> customXPaths) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        this.dynamicXPaths = new HashMap<>(customXPaths);
        initializeDefaultXPaths();
    }

    /**
     * Initialize default XPaths if not provided
     */
    private void initializeDefaultXPaths() {
        dynamicXPaths.putIfAbsent("category", DEFAULT_CATEGORY_XPATH);
        dynamicXPaths.putIfAbsent("entityType", DEFAULT_ENTITY_TYPE_XPATH);
        dynamicXPaths.putIfAbsent("state", DEFAULT_STATE_XPATH);
        dynamicXPaths.putIfAbsent("county", DEFAULT_COUNTY_XPATH);
    }

    /**
     * Set custom XPath for a specific element
     */
    public void setCustomXPath(String elementType, String xpath) {
        dynamicXPaths.put(elementType, xpath);
        System.out.println("✅ Updated XPath for " + elementType + ": " + xpath);
    }

    /**
     * Set multiple custom XPaths at once
     */
    public void setCustomXPaths(Map<String, String> xpaths) {
        dynamicXPaths.putAll(xpaths);
        System.out.println("✅ Updated " + xpaths.size() + " custom XPaths");
    }

    /**
     * Get XPath for a specific element type
     */
    private String getXPath(String elementType) {
        return dynamicXPaths.getOrDefault(elementType, "");
    }

    /**
     * Applies filters on the UI: Category, Entity Type, State, and optionally County
     * Uses dynamic XPaths that can be configured per test execution
     */
    public void applyFilters(String category, String entityType, String state, String county) throws InterruptedException {
        try {
            System.out.println("🔄 Applying filters: Category=" + category + ", EntityType=" + entityType +
                             ", State=" + state + ", County=" + county);

            // Apply Category filter
            if (category != null && !category.isEmpty()) {
                selectFromDropdownByXPath(getXPath("category"), category, "Category");
                Thread.sleep(1000); // Wait for UI to update
            }

            // Apply Entity Type filter
            if (entityType != null && !entityType.isEmpty()) {
                selectFromDropdownByXPath(getXPath("entityType"), entityType, "Entity Type");
                Thread.sleep(1000); // Wait for UI to update
            }

            // Apply State filter
            if (state != null && !state.isEmpty()) {
                selectFromDropdownByXPath(getXPath("state"), state, "State");
                Thread.sleep(2000); // Wait for product list to load
            }

            // Apply County filter (optional)
            if (county != null && !county.isEmpty()) {
                try {
                    selectFromDropdownByXPath(getXPath("county"), county, "County");
                    Thread.sleep(1000); // Wait for UI to update
                } catch (Exception e) {
                    System.out.println("⚠️ County filter not available or failed: " + e.getMessage());
                }
            }

            System.out.println("✅ Filters applied successfully");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw e;
        } catch (Exception e) {
            System.err.println("❌ Error applying filters: " + e.getMessage());
            throw new RuntimeException("Failed to apply filters", e);
        }
    }

    /**
     * Applies filters using custom XPaths provided at runtime
     */
    public void applyFiltersWithCustomXPaths(String category, String entityType, String state, String county,
                                           String categoryXPath, String entityTypeXPath, String stateXPath, String countyXPath) throws InterruptedException {
        // Update XPaths for this execution
        if (categoryXPath != null && !categoryXPath.isEmpty()) {
            setCustomXPath("category", categoryXPath);
        }
        if (entityTypeXPath != null && !entityTypeXPath.isEmpty()) {
            setCustomXPath("entityType", entityTypeXPath);
        }
        if (stateXPath != null && !stateXPath.isEmpty()) {
            setCustomXPath("state", stateXPath);
        }
        if (countyXPath != null && !countyXPath.isEmpty()) {
            setCustomXPath("county", countyXPath);
        }

        // Apply filters with updated XPaths
        applyFilters(category, entityType, state, county);
    }

    /**
     * Selects an option from a dropdown using XPath string
     */
    private void selectFromDropdownByXPath(String dropdownXPath, String optionText, String dropdownName) {
        try {
            if (dropdownXPath == null || dropdownXPath.isEmpty()) {
                throw new RuntimeException("XPath for " + dropdownName + " dropdown is not configured");
            }

            System.out.println("🔄 Selecting '" + optionText + "' from " + dropdownName + " using XPath: " + dropdownXPath);

            // Click dropdown to open
            WebElement dropdown = wait.until(ExpectedConditions.elementToBeClickable(By.xpath(dropdownXPath)));
            scrollIntoView(dropdown);
            dropdown.click();

            try {
                Thread.sleep(500); // Wait for dropdown to open
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("⚠️ Dropdown wait interrupted");
            }

            // Try multiple option selection strategies
            boolean optionSelected = trySelectOption(optionText, dropdownName);

            if (!optionSelected) {
                throw new RuntimeException("Could not find option '" + optionText + "' in " + dropdownName + " dropdown");
            }

            System.out.println("✅ Selected '" + optionText + "' from " + dropdownName + " dropdown");

        } catch (Exception e) {
            System.err.println("❌ Failed to select '" + optionText + "' from " + dropdownName + " dropdown: " + e.getMessage());
            throw new RuntimeException("Dropdown selection failed", e);
        }
    }

    /**
     * Tries multiple strategies to select an option from dropdown
     */
    private boolean trySelectOption(String optionText, String dropdownName) {
        String[] optionPatterns = {
            "//li[contains(text(),'" + optionText + "')]",
            "//li[contains(@data-label,'" + optionText + "')]",
            "//option[contains(text(),'" + optionText + "')]",
            "//div[contains(text(),'" + optionText + "')]",
            "//span[contains(text(),'" + optionText + "')]",
            "//*[contains(text(),'" + optionText + "') and (name()='li' or name()='option' or name()='div' or name()='span')]"
        };

        for (String pattern : optionPatterns) {
            try {
                WebElement option = wait.until(ExpectedConditions.elementToBeClickable(By.xpath(pattern)));
                option.click();
                System.out.println("✅ Option selected using pattern: " + pattern);
                return true;
            } catch (Exception e) {
                System.out.println("⚠️ Pattern failed: " + pattern + " - " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * Legacy method - kept for backward compatibility
     */
    @SuppressWarnings("unused")
    private void selectFromDropdown(By dropdownLocator, String optionText, String dropdownName) {
        try {
            // Click dropdown to open
            WebElement dropdown = wait.until(ExpectedConditions.elementToBeClickable(dropdownLocator));
            scrollIntoView(dropdown);
            dropdown.click();

            try {
                Thread.sleep(500); // Wait for dropdown to open
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("⚠️ Dropdown wait interrupted");
            }

            // Find and click the option
            By optionLocator = By.xpath("//li[contains(text(),'" + optionText + "') or contains(@data-label,'" + optionText + "')]");
            WebElement option = wait.until(ExpectedConditions.elementToBeClickable(optionLocator));
            option.click();

            System.out.println("✅ Selected '" + optionText + "' from " + dropdownName + " dropdown");

        } catch (Exception e) {
            System.err.println("❌ Failed to select '" + optionText + "' from " + dropdownName + " dropdown: " + e.getMessage());
            throw new RuntimeException("Dropdown selection failed", e);
        }
    }

    /**
     * Captures all visible product names from the UI after filters are applied
     */
    public List<String> captureActualProducts() {
        List<String> products = new ArrayList<>();

        try {
            System.out.println("🔄 Capturing actual products from UI...");

            // Wait for products to load
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("⚠️ Product capture wait interrupted");
            }

            // Try multiple strategies to find products
            products = tryMultipleProductCaptureMethods();

            if (products.isEmpty()) {
                System.out.println("⚠️ No products found with standard locators, trying alternative methods...");
                products = captureProductsWithAlternativeMethods();
            }

            // Clean up product names
            products = cleanProductNames(products);

            System.out.println("✅ Captured " + products.size() + " products from UI: " + products);

        } catch (Exception e) {
            System.err.println("❌ Error capturing products from UI: " + e.getMessage());
            e.printStackTrace();
        }

        return products;
    }

    /**
     * Tries multiple methods to capture products from the UI using dynamic patterns
     * Specifically optimized for your product-card structure
     */
    private List<String> tryMultipleProductCaptureMethods() {
        List<String> products = new ArrayList<>();

        // Method 1: Direct search for your specific product-card structure
        try {
            List<WebElement> productCards = driver.findElements(By.xpath("//div[@class='product-card']"));
            if (!productCards.isEmpty()) {
                System.out.println("✅ Found " + productCards.size() + " product cards using direct search");

                for (WebElement card : productCards) {
                    String productName = extractProductNameFromCard(card);
                    if (!productName.isEmpty() && !products.contains(productName)) {
                        products.add(productName);
                    }
                }

                if (!products.isEmpty()) {
                    System.out.println("✅ Method 1: Extracted " + products.size() + " product names from cards");
                    return products;
                }
            }
        } catch (Exception e) {
            System.out.println("⚠️ Method 1 (direct product-card search) failed: " + e.getMessage());
        }

        // Method 2: Look for product container and items within it
        for (String containerPattern : PRODUCT_CONTAINER_PATTERNS) {
            try {
                WebElement productContainer = driver.findElement(By.xpath(containerPattern));
                System.out.println("✅ Found product container using: " + containerPattern);

                for (String itemPattern : PRODUCT_ITEM_PATTERNS) {
                    try {
                        List<WebElement> items = productContainer.findElements(By.xpath(itemPattern));
                        if (!items.isEmpty()) {
                            for (WebElement item : items) {
                                String productName = extractProductNameFromCard(item);
                                if (!productName.isEmpty() && !products.contains(productName)) {
                                    products.add(productName);
                                }
                            }
                            if (!products.isEmpty()) {
                                System.out.println("✅ Method 2: Found " + products.size() + " products using container: " + containerPattern + " and items: " + itemPattern);
                                return products;
                            }
                        }
                    } catch (Exception e) {
                        // Continue to next item pattern
                    }
                }
            } catch (Exception e) {
                // Continue to next container pattern
            }
        }

        // Method 3: Direct search for product items using all patterns
        for (String itemPattern : PRODUCT_ITEM_PATTERNS) {
            try {
                List<WebElement> items = driver.findElements(By.xpath(itemPattern));
                if (!items.isEmpty()) {
                    for (WebElement item : items) {
                        String productName = extractProductNameFromCard(item);
                        if (!productName.isEmpty() && !products.contains(productName)) {
                            products.add(productName);
                        }
                    }
                    if (!products.isEmpty()) {
                        System.out.println("✅ Method 3: Found " + products.size() + " products using direct search: " + itemPattern);
                        return products;
                    }
                }
            } catch (Exception e) {
                // Continue to next pattern
            }
        }

        return products;
    }

    /**
     * Extracts product name from a product card element
     * Handles your specific HTML structure: div.product-card > div.product-content > div.product-name
     */
    private String extractProductNameFromCard(WebElement cardElement) {
        // Try multiple strategies to extract product name

        // Strategy 1: Your exact structure - div.product-name
        try {
            WebElement nameElement = cardElement.findElement(By.xpath(".//div[@class='product-name']"));
            String productName = nameElement.getText().trim();
            if (!productName.isEmpty()) {
                return productName;
            }
        } catch (Exception e) {
            // Continue to next strategy
        }

        // Strategy 2: Try all product name patterns
        for (String namePattern : PRODUCT_NAME_PATTERNS) {
            try {
                WebElement nameElement = cardElement.findElement(By.xpath(namePattern));
                String productName = nameElement.getText().trim();
                if (!productName.isEmpty()) {
                    return productName;
                }
            } catch (Exception e) {
                // Continue to next pattern
            }
        }

        // Strategy 3: Fallback - get text from the entire card and try to extract product name
        try {
            String fullText = cardElement.getText().trim();
            if (!fullText.isEmpty()) {
                // Split by lines and take the first meaningful line (usually the product name)
                String[] lines = fullText.split("\\n");
                for (String line : lines) {
                    line = line.trim();
                    if (!line.isEmpty() && line.length() > 3 && line.length() < 100) {
                        // Skip price patterns and common UI text
                        if (!line.matches(".*\\$\\d+.*") &&
                            !line.toLowerCase().contains("know more") &&
                            !line.toLowerCase().contains("add to cart") &&
                            !line.toLowerCase().contains("our team")) {
                            return line;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Continue
        }

        return ""; // Return empty if no product name found
    }

    /**
     * Alternative methods to capture products when standard methods fail
     */
    private List<String> captureProductsWithAlternativeMethods() {
        List<String> products = new ArrayList<>();

        // Method 3: Look for any elements that might contain product names using broad patterns
        String[] alternativePatterns = {
            "//div[contains(@class,'card')]",
            "//div[contains(@class,'item')]",
            "//li",
            "//span[contains(text(),'Product')]",
            "//div[contains(text(),'Product')]",
            "//div[contains(@class,'title')]",
            "//span[contains(@class,'name')]",
            "//div[contains(@class,'label')]",
            "//p[contains(@class,'product')]"
        };

        for (String pattern : alternativePatterns) {
            try {
                List<WebElement> elements = driver.findElements(By.xpath(pattern));
                for (WebElement element : elements) {
                    String text = element.getText().trim();
                    if (!text.isEmpty() && text.length() > 2 && text.length() < 100) {
                        // Filter out common UI elements that are not products
                        if (!isUIElement(text) && !products.contains(text)) {
                            products.add(text);
                        }
                    }
                }
                if (!products.isEmpty()) {
                    System.out.println("✅ Method 3: Found " + products.size() + " products using pattern: " + pattern);
                    break; // Stop at first successful pattern
                }
            } catch (Exception e) {
                // Continue to next pattern
            }
        }

        // Method 4: Look for specific text patterns that might be products
        if (products.isEmpty()) {
            try {
                List<WebElement> textElements = driver.findElements(By.xpath("//*[contains(text(),'LLC') or contains(text(),'Corp') or contains(text(),'Formation') or contains(text(),'Service')]"));
                for (WebElement element : textElements) {
                    String text = element.getText().trim();
                    if (!text.isEmpty() && text.length() > 5 && text.length() < 100) {
                        if (!isUIElement(text) && !products.contains(text)) {
                            products.add(text);
                        }
                    }
                }
                System.out.println("✅ Method 4: Found " + products.size() + " text-based products");
            } catch (Exception e) {
                System.out.println("⚠️ Method 4 failed: " + e.getMessage());
            }
        }

        return products;
    }

    /**
     * Captures products using custom XPath provided at runtime
     */
    public List<String> captureProductsWithCustomXPath(String customProductXPath) {
        List<String> products = new ArrayList<>();

        if (customProductXPath == null || customProductXPath.isEmpty()) {
            System.out.println("⚠️ No custom XPath provided, using default methods");
            return captureActualProducts();
        }

        try {
            System.out.println("🔄 Capturing products using custom XPath: " + customProductXPath);

            List<WebElement> elements = driver.findElements(By.xpath(customProductXPath));
            for (WebElement element : elements) {
                String productName = element.getText().trim();
                if (!productName.isEmpty() && !products.contains(productName)) {
                    products.add(productName);
                }
            }

            System.out.println("✅ Captured " + products.size() + " products using custom XPath");

        } catch (Exception e) {
            System.err.println("❌ Error capturing products with custom XPath: " + e.getMessage());
            // Fallback to default methods
            return captureActualProducts();
        }

        return cleanProductNames(products);
    }

    /**
     * Checks if a text string is likely a UI element rather than a product name
     */
    private boolean isUIElement(String text) {
        String lowerText = text.toLowerCase();
        return lowerText.contains("select") ||
               lowerText.contains("dropdown") ||
               lowerText.contains("button") ||
               lowerText.contains("click") ||
               lowerText.contains("filter") ||
               lowerText.contains("search") ||
               lowerText.equals("category") ||
               lowerText.equals("state") ||
               lowerText.equals("county") ||
               lowerText.equals("entity type");
    }

    /**
     * Cleans up product names by removing extra whitespace and duplicates
     */
    private List<String> cleanProductNames(List<String> products) {
        List<String> cleanedProducts = new ArrayList<>();

        for (String product : products) {
            String cleaned = product.trim().replaceAll("\\s+", " ");
            if (!cleaned.isEmpty() && !cleanedProducts.contains(cleaned)) {
                cleanedProducts.add(cleaned);
            }
        }

        return cleanedProducts;
    }

    /**
     * Scrolls an element into view
     */
    private void scrollIntoView(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Takes a screenshot for debugging purposes
     */
    public void takeScreenshot(String fileName) {
        try {
            // Implementation would depend on your screenshot utility
            System.out.println("📸 Screenshot taken: " + fileName);
        } catch (Exception e) {
            System.err.println("❌ Failed to take screenshot: " + e.getMessage());
        }
    }

    /**
     * Waits for the page to load completely
     */
    public void waitForPageLoad() {
        try {
            wait.until(webDriver -> ((JavascriptExecutor) webDriver)
                    .executeScript("return document.readyState").equals("complete"));
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("⚠️ Page load wait interrupted");
            }
        } catch (Exception e) {
            System.out.println("⚠️ Page load wait failed: " + e.getMessage());
        }
    }
}
