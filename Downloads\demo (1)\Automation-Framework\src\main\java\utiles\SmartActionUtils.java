package utiles;

import net.datafaker.Faker;
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;
import drivers.ConfigManager;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

public class SmartActionUtils {

    private final WebDriver driver;
    private final WebDriverWait wait;
    private final Faker faker;
    private final ExcelUtils excelUtils;
    private final String filePath;
    private final String sheetName;
    private final int xpathColumn;
    private final int dataColumn;
    private String lastGeneratedPassword = "";

    public SmartActionUtils(WebDriver driver) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        this.faker = new Faker();
        this.excelUtils = new ExcelUtils();

        Properties props = ConfigManager.getProperties();
        this.filePath = props.getProperty("excel.filePath");
        this.sheetName = props.getProperty("excel.sheetName");
        this.xpathColumn = Integer.parseInt(props.getProperty("excel.xpathColumn", "1"));
        this.dataColumn = Integer.parseInt(props.getProperty("excel.dataColumn", "2"));

        ensureScreenshotDirExists();
    }

    private void ensureScreenshotDirExists() {
        File dir = new File("screenshots");
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    private void scrollIntoView(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
    }

    // ✅ Called with row number only
    public void performSmartActionWithRetry(int rowNum) {
        try {
            String xpath = excelUtils.getCellData(filePath, sheetName, rowNum, xpathColumn);
            String manualValue = excelUtils.getCellData(filePath, sheetName, rowNum, dataColumn);

            if (xpath == null || xpath.trim().isEmpty()) {
                System.out.println("⚠️ Skipped empty XPath at row: " + rowNum);
                return;
            }

            performSmartActionWithRetry(By.xpath(xpath), manualValue.isEmpty() ? null : manualValue);

        } catch (Exception e) {
            System.err.println("❌ Error at row " + rowNum + ": " + e.getMessage());
        }
    }

    // ✅ Original method with locator only, uses Faker only
    public void performSmartActionWithRetry(By locator) {
        performSmartActionWithRetry(locator, null);
    }

    // ✅ New method: support manualValue override if available
    public void performSmartActionWithRetry(By locator, String manualValue) {
        try {
            WebElement element = wait.until(ExpectedConditions.presenceOfElementLocated(locator));
            scrollIntoView(element);

            String tag = element.getTagName().toLowerCase();
            String type = safeGetAttribute(element, "type").toLowerCase();
            String combinedAttr = (
                    safeGetAttribute(element, "name") + " " +
                            safeGetAttribute(element, "id") + " " +
                            safeGetAttribute(element, "placeholder") + " " +
                            safeGetAttribute(element, "aria-label")
            ).toLowerCase();

            if ("input".equals(tag) || "textarea".equals(tag)) {
                if ("checkbox".equals(type) || "radio".equals(type)) {
                    if (!element.isSelected()) {
                        element.click();
                    }
                } else {
                    boolean success = false;
                    int attempts = 0;
                    while (!success && attempts < 3) {
                        String valueToUse = manualValue != null ? manualValue : generateSmartFakeData(combinedAttr);
                        element.clear();
                        element.sendKeys(valueToUse);
                        Thread.sleep(500); // allow time for validation

                        if (hasValidationError(element)) {
                            captureErrorAndScreenshot(element, valueToUse);
                            attempts++;
                        } else {
                            success = true;
                        }
                    }
                }
            } else if ("select".equals(tag)) {
                Select dropdown = new Select(element);
                if (dropdown.getOptions().size() > 1) {
                    dropdown.selectByIndex(1);
                }
            } else if ("button".equals(tag) || "a".equals(tag) || "div".equals(tag)) {
                element.click();
            } else {
                System.out.println("Unsupported tag: " + tag + " for locator: " + locator);
            }

        } catch (TimeoutException e) {
            System.err.println("Element not found: " + locator);
        } catch (Exception e) {
            System.err.println("Unexpected error on " + locator + ": " + e.getMessage());
        }
    }

    private String safeGetAttribute(WebElement element, String attr) {
        try {
            String val = element.getAttribute(attr);
            return val == null ? "" : val;
        } catch (Exception e) {
            return "";
        }
    }

    private boolean hasValidationError(WebElement element) {
        try {
            WebElement container = element.findElement(By.xpath("ancestor::div[1]"));
            WebElement error = container.findElement(By.xpath(".//*[contains(@class, 'error') or contains(@class, 'invalid')]"));
            return error.isDisplayed();
        } catch (NoSuchElementException ignored) {
            return false;
        }
    }

    private void captureErrorAndScreenshot(WebElement element, String valueAttempted) {
        String field = safeGetAttribute(element, "name");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        File target = new File("screenshots/" + field + "_" + timestamp + ".png");

        try {
            Files.copy(screenshot.toPath(), target.toPath());
            System.out.println("❌ Validation failed for [" + field + "] with [" + valueAttempted + "]");
            System.out.println("📸 Screenshot: " + target.getAbsolutePath());
        } catch (IOException e) {
            System.err.println("Screenshot error: " + e.getMessage());
        }
    }

    private static final Properties config = new Properties();

    static {
        try {
            FileInputStream fis = new FileInputStream("config.properties");
            config.load(fis);
        } catch (IOException e) {
            throw new RuntimeException("❌ Failed to load config.properties: " + e.getMessage());
        }
    }

    private String getConfig(String key) {
        return config.getProperty(key);
    }


    private String generateSmartFakeData(String combinedAttr) {
        if (combinedAttr.contains("email")) {
            return faker.name().username().replaceAll("\\s+", "") + "@testmail.com";
        }
        if (combinedAttr.contains("password")) {
            lastGeneratedPassword = faker.internet().password(8, 12, true, true);
            return lastGeneratedPassword;
        }
        if (combinedAttr.contains("confirm")) {
            return lastGeneratedPassword;
        }
        if (combinedAttr.contains("name")) {
            return faker.name().fullName();
        }
        if (combinedAttr.contains("phone") || combinedAttr.contains("mobile")) {
            return faker.phoneNumber().cellPhone();
        }
        if (combinedAttr.contains("company") || combinedAttr.contains("organization")) {
            return faker.company().name();
        }
        if (combinedAttr.contains("address")) {
            return faker.address().fullAddress();
        }
        if (combinedAttr.contains("city")) {
            return faker.address().city();
        }
        if (combinedAttr.contains("zip") || combinedAttr.contains("postal")) {
            return faker.address().zipCode();
        }
        if (combinedAttr.contains("date")) {
            return faker.date().birthday().toString();
        }
        if (combinedAttr.contains("website") || combinedAttr.contains("url")) {
            return faker.internet().url();
        }
        return faker.lorem().word();
    }
}
