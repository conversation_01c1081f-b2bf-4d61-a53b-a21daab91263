package utiles;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;

public class ExcelUtils implements Excel {

    private static Workbook workbook;
    private static Sheet sheet;
    private static String userDirectory = System.getProperty("user.dir");

    private static void openWorkbook(String filePath) {
        String fullPath = userDirectory + "/" + filePath;
        try (FileInputStream inputStream = new FileInputStream(fullPath)) {
            workbook = new XSSFWorkbook(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("Failed to open the Excel workbook: " + e.getMessage(), e);
        }
    }

    public int getRowCount(String filePath, String sheetName) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            return sheet.getLastRowNum();
        } finally {
            closeWorkbook();
        }
    }

    public String getCellData(String filePath, String sheetName, int rowNum, int colNum) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new RuntimeException("Sheet " + sheetName + " does not exist in the workbook.");
            }
            DataFormatter formatter = new DataFormatter();
            return formatter.formatCellValue(sheet.getRow(rowNum - 1).getCell(colNum - 1));
        } finally {
            closeWorkbook();
        }
    }

    public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value, boolean testPassed) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            Cell cell = sheet.getRow(rowNum - 1).createCell(colNum - 1);
            cell.setCellValue(value);

            CellStyle style = workbook.createCellStyle();
            if (testPassed) {
                style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            } else {
                style.setFillForegroundColor(IndexedColors.RED.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }
            cell.setCellStyle(style);

            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to set cell data: " + e.getMessage(), e);
        } finally {
            closeWorkbook();
        }
    }

    public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            sheet.getRow(rowNum - 1).createCell(colNum - 1).setCellValue(value);

            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to set cell data: " + e.getMessage(), e);
        } finally {
            closeWorkbook();
        }
    }

    private static void closeWorkbook() {
        if (workbook != null) {
            try {
                workbook.close();
            } catch (IOException e) {
                throw new RuntimeException("Failed to close the Excel workbook: " + e.getMessage(), e);
            } finally {
                workbook = null; // Ensure that the workbook is null after closing
            }
        }
    }
    /**
     * Legacy method for selecting entity and state (kept for backward compatibility)
     */
    public void selectEntityAndState(WebDriver driver, String entityType, String state) throws InterruptedException {
        // Click "Select Entity Type"
        driver.findElement(By.xpath("//label[contains(text(),'Select Entity Type')]/following::div[contains(@class,'p-dropdown')]")).click();
        Thread.sleep(500);
        driver.findElement(By.xpath("//li[contains(text(),'" + entityType + "')]")).click();
        Thread.sleep(500);

        // Click "Select State"
        driver.findElement(By.xpath("//label[contains(text(),'Select State')]/following::div[contains(@class,'p-dropdown')]")).click();
        Thread.sleep(500);
        driver.findElement(By.xpath("//li[contains(text(),'" + state + "')]")).click();
        Thread.sleep(2000); // wait for product list to load
    }

    /**
     * Gets all sheet names from an Excel file
     */
    public List<String> getSheetNames(String filePath) {
        List<String> sheetNames = new ArrayList<>();
        try {
            openWorkbook(filePath);
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                sheetNames.add(workbook.getSheetName(i));
            }
        } finally {
            closeWorkbook();
        }
        return sheetNames;
    }

    /**
     * Gets all values from a specific row (useful for headers)
     */
    public List<String> getRowData(String filePath, String sheetName, int rowNum) {
        List<String> rowData = new ArrayList<>();
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new RuntimeException("Sheet " + sheetName + " does not exist in the workbook.");
            }

            Row row = sheet.getRow(rowNum - 1);
            if (row != null) {
                DataFormatter formatter = new DataFormatter();
                for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                    Cell cell = row.getCell(colIndex);
                    String cellValue = cell != null ? formatter.formatCellValue(cell) : "";
                    rowData.add(cellValue);
                }
            }
        } finally {
            closeWorkbook();
        }
        return rowData;
    }

    /**
     * Gets all values from a specific column
     */
    public List<String> getColumnData(String filePath, String sheetName, int colNum) {
        List<String> columnData = new ArrayList<>();
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new RuntimeException("Sheet " + sheetName + " does not exist in the workbook.");
            }

            DataFormatter formatter = new DataFormatter();
            for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    Cell cell = row.getCell(colNum - 1);
                    String cellValue = cell != null ? formatter.formatCellValue(cell) : "";
                    columnData.add(cellValue);
                }
            }
        } finally {
            closeWorkbook();
        }
        return columnData;
    }

    /**
     * Finds the column index for a specific header value
     */
    public int findColumnIndex(String filePath, String sheetName, String headerValue) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                return -1;
            }

            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                DataFormatter formatter = new DataFormatter();
                for (int colIndex = 0; colIndex < headerRow.getLastCellNum(); colIndex++) {
                    Cell cell = headerRow.getCell(colIndex);
                    if (cell != null) {
                        String cellValue = formatter.formatCellValue(cell);
                        if (cellValue.equalsIgnoreCase(headerValue)) {
                            return colIndex + 1; // Return 1-based index
                        }
                    }
                }
            }
        } finally {
            closeWorkbook();
        }
        return -1;
    }

    /**
     * Checks if a sheet exists in the workbook
     */
    public boolean sheetExists(String filePath, String sheetName) {
        try {
            openWorkbook(filePath);
            return workbook.getSheet(sheetName) != null;
        } finally {
            closeWorkbook();
        }
    }

}
