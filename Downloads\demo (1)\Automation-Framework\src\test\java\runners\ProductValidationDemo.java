package runners;

import drivers.DriverManager;
import models.ProductComparisonResult;
import pages.ProductValidator;
// import utiles.SmartActionUtils; // Not needed - using TestCaseExecutor

import java.util.Arrays;
import java.util.List;

/**
 * Demo class to showcase the Product Validation functionality
 * This can be run independently to test the validation flow
 */
public class ProductValidationDemo {

    public static void main(String[] args) {
        System.out.println("🚀 Starting Product Validation Demo");
        System.out.println("=" + "=".repeat(50));

        try {
            // Initialize WebDriver
            DriverManager.initDriver();

            // Perform login (if needed)
            performLogin();

            // Initialize validator
            ProductValidator validator = new ProductValidator();

            // Demo 1: Test specific entity/state/category combination
            System.out.println("\n📋 Demo 1: Testing specific combination");
            testSpecificCombination(validator);

            // Demo 2: Test with custom expected products
            System.out.println("\n📋 Demo 2: Testing with custom expected products");
            testWithCustomProducts(validator);

            // Demo 3: Quick validation
            System.out.println("\n📋 Demo 3: Quick validation test");
            testQuickValidation(validator);

            // Demo 4: Dynamic order (tests all categories)
            System.out.println("\n📋 Demo 4: Dynamic order validation");
            testDynamicOrder(validator);

            System.out.println("\n✅ Product Validation Demo Completed Successfully!");

        } catch (Exception e) {
            System.err.println("❌ Demo failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
        }
    }

    private static void performLogin() {
        try {
            System.out.println("🔐 Performing dynamic login using TC001...");

            // Use dynamic test case executor to run TC001 (Sign-In)
            utils.TestCaseExecutor testExecutor = new utils.TestCaseExecutor();
            boolean loginResult = testExecutor.executeTestCase("TC001");

            if (loginResult) {
                System.out.println("✅ Login (TC001) completed successfully");
                Thread.sleep(3000); // Wait for login to complete
            } else {
                System.out.println("⚠️ Login (TC001) failed or not needed");
            }

        } catch (Exception e) {
            System.out.println("⚠️ Login failed or not needed: " + e.getMessage());
        }
    }

    private static void testSpecificCombination(ProductValidator validator) {
        try {
            System.out.println("Testing: LLC - Alaska - Entity Formation");

            ProductComparisonResult result = validator.validateProductsForCategory(
                "LLC",
                "Alaska",
                "Entity Formation",
                null
            );

            System.out.println("Result: " + (result.isTestPassed() ? "PASSED ✅" : "FAILED ❌"));
            System.out.println("Expected products: " + result.getExpectedProducts().size());
            System.out.println("Actual products: " + result.getActualProducts().size());

            if (!result.isTestPassed()) {
                System.out.println("Missing: " + result.getMissingProducts());
                System.out.println("Extra: " + result.getExtraProducts());
            }

        } catch (Exception e) {
            System.err.println("❌ Specific combination test failed: " + e.getMessage());
        }
    }

    private static void testWithCustomProducts(ProductValidator validator) {
        try {
            System.out.println("Testing with custom expected products");

            List<String> customExpectedProducts = Arrays.asList(
                "Basic LLC Formation",
                "Registered Agent Service",
                "EIN Service"
            );

            ProductComparisonResult result = validator.validateProducts(
                "LLC",
                "Alaska",
                "Entity Formation",
                null,
                customExpectedProducts
            );

            System.out.println("Result: " + (result.isTestPassed() ? "PASSED ✅" : "FAILED ❌"));
            System.out.println("Custom expected: " + customExpectedProducts);
            System.out.println("Actual from UI: " + result.getActualProducts());

        } catch (Exception e) {
            System.err.println("❌ Custom products test failed: " + e.getMessage());
        }
    }

    private static void testQuickValidation(ProductValidator validator) {
        try {
            System.out.println("Running quick validation");

            boolean result = validator.quickValidate("LLC", "Alaska", "Entity Formation");
            System.out.println("Quick validation result: " + (result ? "PASSED ✅" : "FAILED ❌"));

        } catch (Exception e) {
            System.err.println("❌ Quick validation test failed: " + e.getMessage());
        }
    }

    private static void testDynamicOrder(ProductValidator validator) {
        try {
            System.out.println("Running dynamic order validation (all categories)");

            // This will test all available categories for LLC in Alaska
            validator.dynamicOrder("Alaska", "LLC");

            System.out.println("✅ Dynamic order validation completed");

        } catch (Exception e) {
            System.err.println("❌ Dynamic order test failed: " + e.getMessage());
        }
    }
}
