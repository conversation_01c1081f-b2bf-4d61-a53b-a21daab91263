package com.automation.framework.core.constants;

/**
 * Framework-wide constants
 */
public class FrameworkConstants {
    
    // Framework information
    public static final String FRAMEWORK_NAME = "Generic Automation Framework";
    public static final String FRAMEWORK_VERSION = "2.0.0";
    public static final String FRAMEWORK_AUTHOR = "Automation Team";
    
    // Timeouts (in seconds)
    public static final int DEFAULT_IMPLICIT_WAIT = 10;
    public static final int DEFAULT_EXPLICIT_WAIT = 30;
    public static final int DEFAULT_PAGE_LOAD_TIMEOUT = 60;
    public static final int DEFAULT_SCRIPT_TIMEOUT = 30;
    
    // Retry settings
    public static final int DEFAULT_RETRY_COUNT = 3;
    public static final int DEFAULT_RETRY_DELAY_MS = 1000;
    
    // File paths
    public static final String DEFAULT_SCREENSHOT_PATH = "test-output/screenshots";
    public static final String DEFAULT_REPORT_PATH = "test-output/reports";
    public static final String DEFAULT_LOG_PATH = "test-output/logs";
    public static final String DEFAULT_TESTDATA_PATH = "src/resources/testdata";
    
    // File extensions
    public static final String EXCEL_EXTENSION = ".xlsx";
    public static final String SCREENSHOT_EXTENSION = ".png";
    public static final String LOG_EXTENSION = ".log";
    public static final String HTML_EXTENSION = ".html";
    
    // Browser settings
    public static final String[] SUPPORTED_BROWSERS = {"chrome", "firefox", "edge", "safari"};
    public static final String DEFAULT_BROWSER = "chrome";
    
    // Database settings
    public static final String[] SUPPORTED_DATABASES = {"mysql", "postgresql", "sqlserver", "oracle"};
    public static final int DEFAULT_DB_CONNECTION_TIMEOUT = 30000;
    public static final int DEFAULT_DB_MAX_POOL_SIZE = 10;
    
    // Excel settings
    public static final int MAX_EXCEL_ROWS = 1048576;
    public static final int MAX_EXCEL_COLUMNS = 16384;
    public static final String DEFAULT_EXCEL_SHEET = "Sheet1";
    
    // Validation settings
    public static final String[] SPECIAL_VALIDATION_VALUES = {
        "NOT_NULL", "NULL", "EMPTY", "NOT_EMPTY", "ANY", "{{TEST_DATA}}"
    };
    
    // UI element types
    public static final String[] UI_ELEMENT_TYPES = {
        "textbox", "button", "dropdown", "checkbox", "radio", "link", "image", "table"
    };
    
    // Test execution modes
    public static final String SEQUENTIAL_MODE = "sequential";
    public static final String PARALLEL_MODE = "parallel";
    public static final String DEFAULT_EXECUTION_MODE = SEQUENTIAL_MODE;
    
    // Report formats
    public static final String HTML_REPORT = "html";
    public static final String EXCEL_REPORT = "excel";
    public static final String JSON_REPORT = "json";
    public static final String PDF_REPORT = "pdf";
    
    // Log levels
    public static final String LOG_LEVEL_DEBUG = "DEBUG";
    public static final String LOG_LEVEL_INFO = "INFO";
    public static final String LOG_LEVEL_WARN = "WARN";
    public static final String LOG_LEVEL_ERROR = "ERROR";
    
    // Status constants
    public static final String STATUS_PASS = "PASS";
    public static final String STATUS_FAIL = "FAIL";
    public static final String STATUS_SKIP = "SKIP";
    public static final String STATUS_NOT_RUN = "NOT_RUN";
    
    // Database validation strategies
    public static final String VALIDATION_STRATEGY_LATEST = "LATEST";
    public static final String VALIDATION_STRATEGY_TIMESTAMP = "TIMESTAMP";
    public static final String VALIDATION_STRATEGY_COMPOSITE = "COMPOSITE";
    public static final String VALIDATION_STRATEGY_CUSTOM = "CUSTOM";
    
    // Private constructor to prevent instantiation
    private FrameworkConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
