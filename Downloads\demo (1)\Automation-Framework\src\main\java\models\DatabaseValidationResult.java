package models;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class to hold database validation results for a complete test case
 */
public class DatabaseValidationResult {
    private String testCaseId;
    private LocalDateTime validationTime;
    private List<DatabaseTestData> validationSteps;
    private boolean overallResult;
    private int totalSteps;
    private int passedSteps;
    private int failedSteps;
    private int skippedSteps;
    private List<String> overallErrors;
    private String summary;

    public DatabaseValidationResult() {
        this.validationSteps = new ArrayList<>();
        this.overallErrors = new ArrayList<>();
        this.validationTime = LocalDateTime.now();
        this.overallResult = true;
    }

    public DatabaseValidationResult(String testCaseId) {
        this();
        this.testCaseId = testCaseId;
    }

    // Getters and Setters
    public String getTestCaseId() {
        return testCaseId;
    }

    public void setTestCaseId(String testCaseId) {
        this.testCaseId = testCaseId;
    }

    public LocalDateTime getValidationTime() {
        return validationTime;
    }

    public void setValidationTime(LocalDateTime validationTime) {
        this.validationTime = validationTime;
    }

    public List<DatabaseTestData> getValidationSteps() {
        return validationSteps;
    }

    public void setValidationSteps(List<DatabaseTestData> validationSteps) {
        this.validationSteps = validationSteps;
        calculateResults();
    }

    public void addValidationStep(DatabaseTestData step) {
        this.validationSteps.add(step);
        calculateResults();
    }

    public boolean isOverallResult() {
        return overallResult;
    }

    public void setOverallResult(boolean overallResult) {
        this.overallResult = overallResult;
    }

    public int getTotalSteps() {
        return totalSteps;
    }

    public int getPassedSteps() {
        return passedSteps;
    }

    public int getFailedSteps() {
        return failedSteps;
    }

    public int getSkippedSteps() {
        return skippedSteps;
    }

    public List<String> getOverallErrors() {
        return overallErrors;
    }

    public void addOverallError(String error) {
        if (error != null && !error.trim().isEmpty()) {
            this.overallErrors.add(error.trim());
        }
    }

    public String getSummary() {
        return summary;
    }

    /**
     * Calculate validation results based on individual steps
     */
    private void calculateResults() {
        totalSteps = validationSteps.size();
        passedSteps = 0;
        failedSteps = 0;
        skippedSteps = 0;
        overallResult = true;

        for (DatabaseTestData step : validationSteps) {
            if (step.hasDatabaseValidation()) {
                if (step.isValidationPassed()) {
                    passedSteps++;
                } else {
                    failedSteps++;
                    overallResult = false;
                }
            } else {
                skippedSteps++;
            }
        }

        generateSummary();
    }

    /**
     * Generate validation summary
     */
    private void generateSummary() {
        StringBuilder sb = new StringBuilder();
        
        sb.append("🔍 Database Validation Results for ").append(testCaseId).append("\n");
        sb.append("⏰ Validation Time: ").append(validationTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        sb.append("📊 Summary:\n");
        sb.append("   Total Steps: ").append(totalSteps).append("\n");
        sb.append("   ✅ Passed: ").append(passedSteps).append("\n");
        sb.append("   ❌ Failed: ").append(failedSteps).append("\n");
        sb.append("   ⏭️ Skipped: ").append(skippedSteps).append(" (no DB validation configured)\n");
        sb.append("   🎯 Overall Result: ").append(overallResult ? "✅ PASSED" : "❌ FAILED").append("\n");

        if (!overallErrors.isEmpty()) {
            sb.append("\n🚨 Overall Errors:\n");
            for (String error : overallErrors) {
                sb.append("   - ").append(error).append("\n");
            }
        }

        if (failedSteps > 0) {
            sb.append("\n❌ Failed Steps Details:\n");
            for (DatabaseTestData step : validationSteps) {
                if (step.hasDatabaseValidation() && !step.isValidationPassed()) {
                    sb.append("   Step: ").append(step.getStepDescription()).append("\n");
                    sb.append("   Table: ").append(step.getTableName()).append("\n");
                    sb.append("   Expected: ").append(String.join(", ", step.getExpectedValues())).append("\n");
                    sb.append("   Actual: ").append(step.getActualValues().toString()).append("\n");
                    if (!step.getValidationErrors().isEmpty()) {
                        sb.append("   Errors: ").append(String.join(", ", step.getValidationErrors())).append("\n");
                    }
                    sb.append("\n");
                }
            }
        }

        if (passedSteps > 0) {
            sb.append("✅ Passed Steps:\n");
            for (DatabaseTestData step : validationSteps) {
                if (step.hasDatabaseValidation() && step.isValidationPassed()) {
                    sb.append("   ✓ ").append(step.getStepDescription())
                      .append(" (Table: ").append(step.getTableName()).append(")\n");
                }
            }
        }

        this.summary = sb.toString();
    }

    /**
     * Get detailed validation report
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=" .repeat(80)).append("\n");
        report.append("DATABASE VALIDATION DETAILED REPORT\n");
        report.append("=" .repeat(80)).append("\n");
        report.append(getSummary()).append("\n");

        if (!validationSteps.isEmpty()) {
            report.append("📋 Step-by-Step Details:\n");
            report.append("-" .repeat(80)).append("\n");
            
            for (int i = 0; i < validationSteps.size(); i++) {
                DatabaseTestData step = validationSteps.get(i);
                report.append("Step ").append(i + 1).append(": ").append(step.getStepDescription()).append("\n");
                
                if (step.hasDatabaseValidation()) {
                    report.append(step.getValidationSummary()).append("\n");
                } else {
                    report.append("   ⏭️ No database validation configured for this step\n\n");
                }
                
                if (i < validationSteps.size() - 1) {
                    report.append("-" .repeat(40)).append("\n");
                }
            }
        }

        report.append("=" .repeat(80)).append("\n");
        return report.toString();
    }

    /**
     * Export results to CSV format
     */
    public String toCsvFormat() {
        StringBuilder csv = new StringBuilder();
        
        // Header
        csv.append("Test Case ID,Step Description,Table Name,Columns Checked,Expected Values,Actual Values,Result,Errors\n");
        
        // Data rows
        for (DatabaseTestData step : validationSteps) {
            if (step.hasDatabaseValidation()) {
                csv.append("\"").append(testCaseId).append("\",");
                csv.append("\"").append(step.getStepDescription()).append("\",");
                csv.append("\"").append(step.getTableName()).append("\",");
                csv.append("\"").append(String.join(";", step.getColumnsToCheck())).append("\",");
                csv.append("\"").append(String.join(";", step.getExpectedValues())).append("\",");
                csv.append("\"").append(step.getActualValues().toString()).append("\",");
                csv.append("\"").append(step.isValidationPassed() ? "PASSED" : "FAILED").append("\",");
                csv.append("\"").append(String.join(";", step.getValidationErrors())).append("\"");
                csv.append("\n");
            }
        }
        
        return csv.toString();
    }

    /**
     * Check if any database validations were performed
     */
    public boolean hasDatabaseValidations() {
        return validationSteps.stream().anyMatch(DatabaseTestData::hasDatabaseValidation);
    }

    @Override
    public String toString() {
        return "DatabaseValidationResult{" +
                "testCaseId='" + testCaseId + '\'' +
                ", overallResult=" + overallResult +
                ", totalSteps=" + totalSteps +
                ", passedSteps=" + passedSteps +
                ", failedSteps=" + failedSteps +
                ", skippedSteps=" + skippedSteps +
                '}';
    }
}
