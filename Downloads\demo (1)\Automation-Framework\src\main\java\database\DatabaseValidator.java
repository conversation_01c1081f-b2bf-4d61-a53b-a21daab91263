package database;

import models.DatabaseTestData;
import models.DatabaseValidationResult;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Database Validator utility class for performing database validations
 */
public class DatabaseValidator {

    /**
     * Validate a single database test step
     */
    public static DatabaseTestData validateStep(DatabaseTestData testData) {
        if (!testData.hasDatabaseValidation()) {
            testData.setValidationMessage("No database validation configured for this step");
            testData.setValidationPassed(true); // Skip validation
            return testData;
        }

        try {
            System.out.println("🔍 Validating database step: " + testData.getStepDescription());
            System.out.println("   Table: " + testData.getTableName());
            System.out.println("   Query: " + testData.generateValidationQuery());

            // Execute the validation query
            executeValidationQuery(testData);

            // Compare expected vs actual values
            compareValues(testData);

        } catch (Exception e) {
            testData.setValidationPassed(false);
            testData.addValidationError("Database validation failed: " + e.getMessage());
            System.err.println("❌ Database validation error: " + e.getMessage());
        }

        return testData;
    }

    /**
     * Execute the validation query and populate actual values
     */
    private static void executeValidationQuery(DatabaseTestData testData) throws SQLException {
        String query = testData.generateValidationQuery();
        
        try (Connection connection = DatabaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {

            if (resultSet.next()) {
                // Record found - extract values
                for (String column : testData.getColumnsToCheck()) {
                    Object value = resultSet.getObject(column);
                    testData.addActualValue(column, value);
                }
                
                System.out.println("   ✅ Record found in database");
                System.out.println("   Actual values: " + testData.getActualValues());
                
            } else {
                // No record found
                testData.setValidationPassed(false);
                testData.addValidationError("No record found matching the WHERE condition: " + testData.getProcessedWhereCondition());
                System.out.println("   ❌ No record found matching the condition");
            }
        }
    }

    /**
     * Compare expected values with actual values from database
     */
    private static void compareValues(DatabaseTestData testData) {
        List<String> expectedValues = testData.getExpectedValues();
        List<String> columnsToCheck = testData.getColumnsToCheck();
        
        if (expectedValues.size() != columnsToCheck.size()) {
            testData.addValidationError("Mismatch between number of columns (" + columnsToCheck.size() + 
                                      ") and expected values (" + expectedValues.size() + ")");
            testData.setValidationPassed(false);
            return;
        }

        boolean allMatched = true;
        List<String> mismatches = new ArrayList<>();

        for (int i = 0; i < columnsToCheck.size(); i++) {
            String column = columnsToCheck.get(i);
            String expectedValue = expectedValues.get(i);
            Object actualValue = testData.getActualValues().get(column);
            
            // Convert actual value to string for comparison
            String actualValueStr = (actualValue != null) ? actualValue.toString() : "null";
            
            // Handle special expected values
            boolean matches = compareValues(expectedValue, actualValueStr, actualValue);
            
            if (!matches) {
                allMatched = false;
                mismatches.add(String.format("Column '%s': expected '%s', actual '%s'", 
                                           column, expectedValue, actualValueStr));
                System.out.println("   ❌ Mismatch in " + column + ": expected '" + expectedValue + "', actual '" + actualValueStr + "'");
            } else {
                System.out.println("   ✅ Match in " + column + ": '" + actualValueStr + "'");
            }
        }

        testData.setValidationPassed(allMatched);
        
        if (allMatched) {
            testData.setValidationMessage("All database values match expected values");
        } else {
            testData.setValidationMessage("Database validation failed - value mismatches found");
            testData.getValidationErrors().addAll(mismatches);
        }
    }

    /**
     * Compare individual expected vs actual values with special handling
     */
    private static boolean compareValues(String expectedValue, String actualValueStr, Object actualValue) {
        // Handle null values
        if ("null".equalsIgnoreCase(expectedValue) || "NULL".equals(expectedValue)) {
            return actualValue == null;
        }
        
        if (actualValue == null) {
            return false;
        }

        // Handle special keywords
        switch (expectedValue.toUpperCase()) {
            case "NOT_NULL":
            case "NOTNULL":
                return actualValue != null;
                
            case "EMPTY":
                return actualValueStr.isEmpty();
                
            case "NOT_EMPTY":
            case "NOTEMPTY":
                return !actualValueStr.isEmpty();
                
            case "ANY":
                return true; // Any value is acceptable
                
            default:
                // Handle boolean values
                if (actualValue instanceof Boolean) {
                    if ("true".equalsIgnoreCase(expectedValue) || "1".equals(expectedValue)) {
                        return (Boolean) actualValue;
                    }
                    if ("false".equalsIgnoreCase(expectedValue) || "0".equals(expectedValue)) {
                        return !(Boolean) actualValue;
                    }
                }
                
                // Handle numeric values
                if (actualValue instanceof Number) {
                    try {
                        double expectedNum = Double.parseDouble(expectedValue);
                        double actualNum = ((Number) actualValue).doubleValue();
                        return Math.abs(expectedNum - actualNum) < 0.0001; // Handle floating point precision
                    } catch (NumberFormatException e) {
                        // Not a number, fall through to string comparison
                    }
                }
                
                // Default string comparison (case-insensitive)
                return expectedValue.equalsIgnoreCase(actualValueStr);
        }
    }

    /**
     * Validate multiple database test steps
     */
    public static DatabaseValidationResult validateSteps(List<DatabaseTestData> testSteps) {
        DatabaseValidationResult result = new DatabaseValidationResult();
        
        if (!testSteps.isEmpty()) {
            result.setTestCaseId(testSteps.get(0).getTestCaseId());
        }

        System.out.println("🚀 Starting database validation for " + testSteps.size() + " steps");

        for (DatabaseTestData step : testSteps) {
            try {
                DatabaseTestData validatedStep = validateStep(step);
                result.addValidationStep(validatedStep);
            } catch (Exception e) {
                step.setValidationPassed(false);
                step.addValidationError("Unexpected error during validation: " + e.getMessage());
                result.addValidationStep(step);
                result.addOverallError("Step validation failed: " + step.getStepDescription() + " - " + e.getMessage());
            }
        }

        System.out.println("✅ Database validation completed");
        System.out.println("   Total steps: " + result.getTotalSteps());
        System.out.println("   Passed: " + result.getPassedSteps());
        System.out.println("   Failed: " + result.getFailedSteps());
        System.out.println("   Skipped: " + result.getSkippedSteps());

        return result;
    }

    /**
     * Test database connectivity
     */
    public static boolean testDatabaseConnection() {
        try {
            System.out.println("🔍 Testing database connection...");
            boolean connected = DatabaseManager.testConnection();
            
            if (connected) {
                System.out.println("✅ Database connection successful");
                
                // Get database metadata
                try (Connection connection = DatabaseManager.getConnection()) {
                    DatabaseMetaData metaData = connection.getMetaData();
                    System.out.println("   Database: " + metaData.getDatabaseProductName() + " " + metaData.getDatabaseProductVersion());
                    System.out.println("   Driver: " + metaData.getDriverName() + " " + metaData.getDriverVersion());
                }
            } else {
                System.out.println("❌ Database connection failed");
            }
            
            return connected;
        } catch (Exception e) {
            System.err.println("❌ Database connection test error: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get list of tables in the database (for debugging/verification)
     */
    public static List<String> getTableList() {
        List<String> tables = new ArrayList<>();
        
        try (Connection connection = DatabaseManager.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet resultSet = metaData.getTables(null, null, "%", new String[]{"TABLE"});
            
            while (resultSet.next()) {
                tables.add(resultSet.getString("TABLE_NAME"));
            }
            
            System.out.println("📋 Found " + tables.size() + " tables in database");
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting table list: " + e.getMessage());
        }
        
        return tables;
    }

    /**
     * Get column information for a specific table (for debugging/verification)
     */
    public static List<String> getTableColumns(String tableName) {
        List<String> columns = new ArrayList<>();
        
        try (Connection connection = DatabaseManager.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet resultSet = metaData.getColumns(null, null, tableName, "%");
            
            while (resultSet.next()) {
                String columnName = resultSet.getString("COLUMN_NAME");
                String columnType = resultSet.getString("TYPE_NAME");
                columns.add(columnName + " (" + columnType + ")");
            }
            
            System.out.println("📋 Table '" + tableName + "' has " + columns.size() + " columns");
            
        } catch (SQLException e) {
            System.err.println("❌ Error getting columns for table '" + tableName + "': " + e.getMessage());
        }
        
        return columns;
    }
}
