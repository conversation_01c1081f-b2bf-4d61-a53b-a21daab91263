package com.automation.framework.data.database;

import com.automation.framework.core.config.FrameworkConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Enhanced Database Connection Manager with improved configuration and error handling
 */
public class DatabaseManager {
    private static HikariDataSource dataSource;
    private static boolean initialized = false;
    private static final FrameworkConfig config = FrameworkConfig.getInstance();

    /**
     * Initialize the database connection pool with framework configuration
     */
    public static synchronized void initialize() {
        if (initialized) {
            return;
        }

        try {
            HikariConfig hikariConfig = new HikariConfig();
            
            // Get database configuration from framework config
            String dbType = config.getDatabaseType();
            String host = config.getDatabaseHost();
            String port = config.getDatabasePort();
            String dbName = config.getDatabaseName();
            String username = config.getDatabaseUsername();
            String password = config.getDatabasePassword();
            
            // Validate required configuration
            validateDatabaseConfig(dbType, host, port, dbName, username, password);
            
            // Build JDBC URL based on database type
            String jdbcUrl = buildJdbcUrl(dbType, host, port, dbName);
            
            hikariConfig.setJdbcUrl(jdbcUrl);
            hikariConfig.setUsername(username);
            hikariConfig.setPassword(password);
            
            // Connection pool settings
            hikariConfig.setMaximumPoolSize(config.getDatabaseMaxPoolSize());
            hikariConfig.setConnectionTimeout(config.getDatabaseConnectionTimeout());
            hikariConfig.setValidationTimeout(5000);
            hikariConfig.setLeakDetectionThreshold(60000);
            hikariConfig.setIdleTimeout(300000); // 5 minutes
            hikariConfig.setMaxLifetime(1800000); // 30 minutes
            
            // Set validation query based on database type
            String validationQuery = getValidationQuery(dbType);
            hikariConfig.setConnectionTestQuery(validationQuery);
            
            // Additional settings for reliability
            hikariConfig.setAutoCommit(true);
            hikariConfig.setConnectionInitSql("SELECT 1");
            
            dataSource = new HikariDataSource(hikariConfig);
            initialized = true;
            
            System.out.println("✅ Database connection pool initialized successfully");
            System.out.println("   Database Type: " + dbType.toUpperCase());
            System.out.println("   Host: " + host + ":" + port);
            System.out.println("   Database: " + dbName);
            System.out.println("   Max Pool Size: " + hikariConfig.getMaximumPoolSize());
            System.out.println("   Connection Timeout: " + hikariConfig.getConnectionTimeout() + "ms");
            
        } catch (Exception e) {
            System.err.println("❌ Failed to initialize database connection pool: " + e.getMessage());
            throw new RuntimeException("Database initialization failed", e);
        }
    }

    /**
     * Get a database connection from the pool
     */
    public static Connection getConnection() throws SQLException {
        if (!initialized) {
            initialize();
        }
        
        if (dataSource == null || dataSource.isClosed()) {
            throw new SQLException("Database connection pool is not available");
        }
        
        return dataSource.getConnection();
    }

    /**
     * Close the connection pool
     */
    public static synchronized void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            initialized = false;
            System.out.println("✅ Database connection pool closed");
        }
    }

    /**
     * Test database connectivity
     */
    public static boolean testConnection() {
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            System.err.println("❌ Database connection test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get current pool status
     */
    public static String getPoolStatus() {
        if (dataSource == null) {
            return "Pool not initialized";
        }
        
        return String.format("Pool Status - Active: %d, Idle: %d, Total: %d, Waiting: %d",
                dataSource.getHikariPoolMXBean().getActiveConnections(),
                dataSource.getHikariPoolMXBean().getIdleConnections(),
                dataSource.getHikariPoolMXBean().getTotalConnections(),
                dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
    }

    /**
     * Validate database configuration
     */
    private static void validateDatabaseConfig(String dbType, String host, String port, 
                                             String dbName, String username, String password) {
        if (dbType == null || dbType.trim().isEmpty()) {
            throw new IllegalArgumentException("Database type is required");
        }
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("Database host is required");
        }
        if (port == null || port.trim().isEmpty()) {
            throw new IllegalArgumentException("Database port is required");
        }
        if (dbName == null || dbName.trim().isEmpty()) {
            throw new IllegalArgumentException("Database name is required");
        }
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Database username is required");
        }
        if (password == null) {
            throw new IllegalArgumentException("Database password is required");
        }
    }

    /**
     * Build JDBC URL based on database type
     */
    private static String buildJdbcUrl(String dbType, String host, String port, String dbName) {
        switch (dbType.toLowerCase()) {
            case "mysql":
                return String.format("*****************************************************************************************************", 
                                   host, port, dbName);
            case "postgresql":
                return String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
            case "sqlserver":
                return String.format("********************************************************************************", 
                                   host, port, dbName);
            case "oracle":
                return String.format("**************************", host, port, dbName);
            default:
                throw new IllegalArgumentException("Unsupported database type: " + dbType);
        }
    }

    /**
     * Get validation query based on database type
     */
    private static String getValidationQuery(String dbType) {
        switch (dbType.toLowerCase()) {
            case "mysql":
            case "postgresql":
                return "SELECT 1";
            case "sqlserver":
                return "SELECT 1";
            case "oracle":
                return "SELECT 1 FROM DUAL";
            default:
                return "SELECT 1";
        }
    }

    /**
     * Get database type from configuration
     */
    public static String getDatabaseType() {
        return config.getDatabaseType();
    }

    /**
     * Check if database is initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * Force reconnection (useful for testing)
     */
    public static synchronized void reconnect() {
        shutdown();
        initialize();
    }
}
