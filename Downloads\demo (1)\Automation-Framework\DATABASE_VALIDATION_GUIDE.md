# 🔍 Database Validation Framework Guide

This guide explains how to use the new **Database Validation Framework** that validates whether UI actions correctly store data in your database.

## 🎯 **What It Does**

The framework automatically validates that:
- **Sign-up data** is correctly stored in the `users` table
- **Order data** is saved in the `orders` table  
- **Profile updates** are reflected in the database
- **Any UI action** results in correct database changes

## 🚀 **Quick Start**

### **Step 1: Configure Database Connection**

Edit `config.properties` and update your database details:

```properties
# Database Configuration
db.type=mysql                    # mysql, postgresql, sqlserver, oracle
db.host=localhost               # Your database host
db.port=3306                    # Your database port
db.name=your_database_name      # Your database name
db.username=your_username       # Database username
db.password=your_password       # Database password
```

### **Step 2: Add Database Validation Columns to Excel**

Open your `FieldInputSheet.xlsx` and add these new columns:

| Column | Name | Description | Example |
|--------|------|-------------|---------|
| **N** | DB_TABLE | Database table name | `users` |
| **O** | DB_COLUMNS | Columns to check (comma-separated) | `email,status,created_at` |
| **P** | DB_WHERE_CONDITION | WHERE condition to find record | `email='{{TEST_DATA}}'` |
| **Q** | DB_EXPECTED_VALUES | Expected values (comma-separated) | `<EMAIL>,active,NOT_NULL` |

### **Step 3: Configure Your Test Cases**

For a **sign-up test case**, your Excel might look like:

| A | F | I | M | N | O | P | Q |
|---|---|---|---|---|---|---|---|
| TC001 | 1. Enter Email | <EMAIL> | //*[@id="email"] | users | email,status | email='{{TEST_DATA}}' | <EMAIL>,active |
| TC001 | 2. Enter Password | MyPass123 | //*[@id="password"] | | | | |
| TC001 | 3. Click Submit | | //button[@type="submit"] | users | email,username,status,created_at | email='<EMAIL>' | <EMAIL>,snehal,active,NOT_NULL |

## 📊 **Excel Configuration Details**

### **Column N: DB_TABLE**
- Specify which database table to check
- Examples: `users`, `orders`, `products`, `user_profiles`
- Leave empty if no database validation needed for that step

### **Column O: DB_COLUMNS** 
- Comma-separated list of columns to validate
- Examples: 
  - `email,status`
  - `order_id,total_amount,status`
  - `first_name,last_name,phone`

### **Column P: DB_WHERE_CONDITION**
- SQL WHERE condition to find the record
- Use `{{TEST_DATA}}` placeholder for dynamic values
- Examples:
  - `email='{{TEST_DATA}}'`
  - `user_id=123 AND status='active'`
  - `order_date >= '2024-01-01'`

### **Column Q: DB_EXPECTED_VALUES**
- Comma-separated expected values (same order as columns)
- Examples:
  - `<EMAIL>,active`
  - `100.50,completed,2024-01-15`

## 🔧 **Special Expected Values**

| Value | Description | Example Use Case |
|-------|-------------|------------------|
| `NOT_NULL` | Field should not be null | `created_at` timestamp |
| `NULL` | Field should be null | Optional fields |
| `EMPTY` | Field should be empty string | Cleared fields |
| `NOT_EMPTY` | Field should not be empty | Required fields |
| `ANY` | Any value is acceptable | Auto-generated IDs |
| `{{TEST_DATA}}` | Use actual test data | Email validation |

## 💻 **How to Use in Code**

### **Method 1: Standalone Database Validation**

```java
// Create database validation executor
DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();

// Validate a specific test case
DatabaseValidationResult result = dbValidator.executeValidation("TC001");

// Check results
if (result.isOverallResult()) {
    System.out.println("✅ Database validation passed!");
} else {
    System.out.println("❌ Database validation failed!");
    System.out.println(result.getSummary());
}
```

### **Method 2: Integrated with Existing Test Execution**

```java
// Your existing test execution
TestCaseExecutor testExecutor = new TestCaseExecutor();
testExecutor.executeTestCase("TC001");

// Add database validation after UI actions
DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
DatabaseValidationResult dbResult = dbValidator.executeValidation("TC001");

// Combined validation
boolean overallSuccess = testExecutor.wasSuccessful() && dbResult.isOverallResult();
```

### **Method 3: TestNG Integration**

```java
@Test
public void testSignupWithDatabaseValidation() {
    // Execute UI test
    TestCaseExecutor testExecutor = new TestCaseExecutor();
    testExecutor.executeTestCase("TC001");
    
    // Validate database
    DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
    DatabaseValidationResult result = dbValidator.executeValidation("TC001");
    
    // Assert results
    Assert.assertTrue(result.isOverallResult(), "Database validation failed: " + result.getSummary());
}
```

## 🎯 **Real-World Examples**

### **Example 1: User Registration Validation**

**Excel Configuration:**
```
TC001 | 3. Click Register | | //button[text()='Register'] | users | email,username,status,created_at | email='<EMAIL>' | <EMAIL>,snehal_user,active,NOT_NULL
```

**What it validates:**
- User record exists in `users` table
- Email is correctly stored
- Username is set properly  
- Status is 'active'
- Created timestamp is not null

### **Example 2: Order Placement Validation**

**Excel Configuration:**
```
TC002 | 5. Place Order | | //button[@id='place-order'] | orders | user_email,total_amount,status,order_date | user_email='<EMAIL>' | <EMAIL>,299.99,pending,NOT_NULL
```

**What it validates:**
- Order record created in `orders` table
- Correct user email associated
- Total amount matches
- Initial status is 'pending'
- Order date is recorded

### **Example 3: Profile Update Validation**

**Excel Configuration:**
```
TC003 | 4. Save Profile | John Doe | //button[@id='save-profile'] | user_profiles | user_id,full_name,updated_at | user_id=123 | 123,John Doe,NOT_NULL
```

## 🔍 **Debugging & Troubleshooting**

### **Test Database Connection**

```java
DatabaseValidationExecutor dbValidator = new DatabaseValidationExecutor();
boolean isValid = dbValidator.validateConfiguration();

if (!isValid) {
    dbValidator.printConfigurationHelp();
}
```

### **Check Available Tables**

```java
List<String> tables = DatabaseValidator.getTableList();
System.out.println("Available tables: " + tables);
```

### **Check Table Columns**

```java
List<String> columns = DatabaseValidator.getTableColumns("users");
System.out.println("Users table columns: " + columns);
```

### **Common Issues & Solutions**

| Issue | Solution |
|-------|----------|
| "Database connection failed" | Check `config.properties` database settings |
| "Table not found" | Verify table name spelling in Excel |
| "Column not found" | Check column names in DB_COLUMNS |
| "No record found" | Verify WHERE condition matches actual data |
| "Value mismatch" | Check expected values format |

## 📋 **Configuration Checklist**

- [ ] Database connection configured in `config.properties`
- [ ] Database validation columns added to Excel (N, O, P, Q)
- [ ] Test cases have database validation steps configured
- [ ] Table names and column names are correct
- [ ] WHERE conditions use proper syntax
- [ ] Expected values match database data types

## 🎉 **Benefits**

✅ **Comprehensive Testing**: Validates both UI and database layers  
✅ **Excel-Driven**: No code changes needed for new validations  
✅ **Multi-Database Support**: Works with MySQL, PostgreSQL, SQL Server, Oracle  
✅ **Flexible Conditions**: Support for complex WHERE clauses  
✅ **Detailed Reporting**: Clear pass/fail results with error details  
✅ **Integration Ready**: Works with existing TestNG/JUnit tests  

## 🔄 **Next Steps**

1. **Configure your database connection**
2. **Add database validation columns to your Excel**
3. **Configure validation for your sign-up test case**
4. **Run the validation and check results**
5. **Expand to other test cases as needed**

Your automation framework now validates the complete flow: **UI Actions → Database Changes** ✅
