# 🎯 Dynamic Test Case Execution Guide

This guide explains how to use the new **Dynamic Test Case Execution** feature that automatically reads test cases from Excel and executes them.

## 🚀 What's New

Instead of hardcoding row numbers, you can now:
- **Execute any test case by ID** (e.g., TC001, TC002, TC003)
- **Automatically read test steps** from your Excel file
- **Run multiple test cases** in sequence
- **Interactive mode** for easy test selection

## 📋 Excel Structure Expected

Your Excel file should have this structure:

| Column A | Column B | ... | Column F | ... | Column I | ... | Column M |
|----------|----------|-----|----------|-----|----------|-----|----------|
| TC001    | Sign-In Page | ... | 1. Enter Email | ... | <EMAIL> | ... | //*[@id="email"] |
| TC001    | Sign-In Page | ... | 2. Enter Password | ... | S<PERSON>hal@123 | ... | //*[@id="password"]/div/input |
| TC001    | Sign-In Page | ... | 3. Submit Button | ... |  | ... | //*[@id="root"]/div/div/div[2]/div[2]/form/button |
| TC002    | Dashboard | ... | 1. Click Menu | ... |  | ... | //button[@class="menu"] |

**Column Mapping:**
- **Column A (1)**: Test Case ID (TC001, TC002, etc.)
- **Column B (2)**: Test Case Name
- **Column F (6)**: Step Description
- **Column I (9)**: Test Data
- **Column M (13)**: XPath

## 🎮 How to Use

### Method 1: Enhanced TestExecutor (Recommended)

```bash
# Run the enhanced TestExecutor
java -cp "target/classes:target/dependency/*" runners.TestExecutor

# Choose option 2 for Dynamic Test Case Execution
# Then enter the test case ID (e.g., TC001)
```

### Method 2: DynamicTestCaseRunner

#### Interactive Mode:
```bash
java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner
```

#### Command Line Mode:
```bash
# Run single test case
java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner TC001

# Run multiple test cases
java -cp "target/classes:target/dependency/*" runners.DynamicTestCaseRunner TC001 TC002 TC003
```

#### Maven Execution:
```bash
# Single test case
mvn exec:java -Dexec.mainClass="runners.DynamicTestCaseRunner" -Dexec.args="TC001"

# Multiple test cases
mvn exec:java -Dexec.mainClass="runners.DynamicTestCaseRunner" -Dexec.args="TC001 TC002"
```

### Method 3: Programmatic Usage

```java
// Initialize the test executor
TestCaseExecutor testExecutor = new TestCaseExecutor();

// Execute a single test case
boolean result = testExecutor.executeTestCase("TC001");

// Execute multiple test cases
boolean allPassed = testExecutor.executeMultipleTestCases("TC001", "TC002", "TC003");

// Get available test cases
List<String> availableTestCases = testExecutor.getAvailableTestCases();
```

## 🔧 Integration with Product Validation

The system now automatically runs TC001 (login) before product validation:

```java
// In your product validation tests
TestCaseExecutor testExecutor = new TestCaseExecutor();
testExecutor.executeTestCase("TC001"); // Login first

// Then run product validation
ProductValidator validator = new ProductValidator();
validator.dynamicOrder("Alaska", "LLC");
```

## 📊 Example Output

```
🚀 Executing Test Case: TC001
📋 Found 3 steps for TC001
🔄 Executing Step 1: 1. Enter Email
✅ Step 1 completed successfully
🔄 Executing Step 2: 2. Enter Password
✅ Step 2 completed successfully
🔄 Executing Step 3: 3. Submit Button
✅ Step 3 completed successfully
✅ Test Case TC001 executed successfully!
```

## 🎯 Features

### ✅ **Automatic Step Detection**
- Reads all rows for a test case ID
- Only executes steps that have XPath values
- Skips empty or header rows

### ✅ **Smart Data Handling**
- Uses test data from Column I when available
- Handles empty test data for button clicks
- Leverages existing SmartActionUtils for robust execution

### ✅ **Error Handling**
- Detailed error messages for each step
- Continues execution even if some steps fail
- Comprehensive logging

### ✅ **Flexible Execution**
- Single test case execution
- Multiple test case batch execution
- Interactive mode for easy selection
- Command line support for automation

## 🔍 Troubleshooting

### Common Issues:

1. **Test Case Not Found**
   ```
   ❌ No test steps found for test case: TC999
   ```
   - Check that the test case ID exists in Column A
   - Ensure the ID is spelled correctly (case-insensitive)

2. **No XPath Found**
   ```
   ⚠️ Skipping row - no XPath found
   ```
   - Ensure Column M has XPath values for action steps
   - Empty XPath rows are automatically skipped

3. **Step Execution Failed**
   ```
   ❌ Step 2 failed: 2. Enter Password
   ```
   - Check that the XPath is correct
   - Verify the element is visible and interactable
   - Check if test data is in the correct format

### Debug Tips:

1. **Check Available Test Cases:**
   ```java
   TestCaseExecutor executor = new TestCaseExecutor();
   List<String> testCases = executor.getAvailableTestCases();
   System.out.println("Available: " + testCases);
   ```

2. **Verify Excel Structure:**
   - Ensure your Excel file is at `src/resources/testdata/FieldInputSheet.xlsx`
   - Check that column indices match your Excel structure
   - Verify test case IDs start with "TC"

3. **Test Individual Steps:**
   - Use SmartActionUtils directly to test individual XPaths
   - Check browser developer tools for correct XPath

## 🚀 Advanced Usage

### Custom Column Configuration

If your Excel has different column structure, update `TestCaseExecutor.java`:

```java
// Update these constants to match your Excel structure
private static final int TEST_CASE_ID_COLUMN = 1;    // Your test case ID column
private static final int STEP_DESCRIPTION_COLUMN = 6; // Your step description column
private static final int TEST_DATA_COLUMN = 9;       // Your test data column
private static final int XPATH_COLUMN = 13;          // Your XPath column
```

### Adding New Test Cases

Simply add new rows to your Excel file:

```
TC002 | Dashboard Test | ... | 1. Click Dashboard | ... | | ... | //a[@href="/dashboard"]
TC002 | Dashboard Test | ... | 2. Verify Title | ... | Dashboard | ... | //h1[@class="title"]
TC003 | Logout Test | ... | 1. Click Logout | ... | | ... | //button[@id="logout"]
```

### Batch Execution

Create test suites by running multiple test cases:

```java
// Login + Product Validation + Logout
testExecutor.executeMultipleTestCases("TC001", "TC002", "TC003");
```

---

**🎉 You can now execute any test case dynamically! Just specify the test case ID and the system will automatically read and execute all steps from your Excel file.**
