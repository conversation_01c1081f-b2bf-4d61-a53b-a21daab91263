package examples;

import drivers.DriverManager;
import models.ProductComparisonResult;
import pages.ProductFilterPage;
import pages.ProductValidator;
import utils.TestCaseExecutor;

import java.util.List;

/**
 * Example class showing how to use the updated system with your specific product card structure
 * 
 * Your HTML structure:
 * <div class="product-card">
 *   <div class="product-content">
 *     <div class="product-name">ARTICLES OF ORGANIZATION</div>
 *     <div class="product-description">Our team of experts will prepare...</div>
 *     <div class="product-price">$99.99</div>
 *   </div>
 *   <div class="product-footer">
 *     <button>Know More</button>
 *     <button>Add To Cart</button>
 *   </div>
 * </div>
 */
public class ProductCardExample {

    public static void main(String[] args) {
        System.out.println("🎯 Product Card Structure Example");
        System.out.println("=" + "=".repeat(50));

        try {
            // Initialize WebDriver
            DriverManager.initDriver();
            
            // Example 1: Basic product capture with your structure
            example1_BasicProductCapture();
            
            // Example 2: Complete validation flow
            example2_CompleteValidation();
            
            // Example 3: Custom XPath for your specific structure
            example3_CustomXPathForYourStructure();
            
        } catch (Exception e) {
            System.err.println("❌ Example failed: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
        }
    }

    /**
     * Example 1: Basic product capture optimized for your product-card structure
     */
    private static void example1_BasicProductCapture() {
        System.out.println("\n📋 Example 1: Basic Product Capture");
        System.out.println("-".repeat(40));
        
        try {
            // Login first
            TestCaseExecutor testExecutor = new TestCaseExecutor();
            testExecutor.executeTestCase("TC001");
            
            // Create filter page
            ProductFilterPage filterPage = new ProductFilterPage(DriverManager.getDriver());
            
            // Apply filters (this will use your dropdown XPaths)
            filterPage.applyFilters("Entity Formation", "LLC", "Alaska", null);
            
            // Capture products - the system is now optimized for your product-card structure
            List<String> products = filterPage.captureActualProducts();
            
            System.out.println("✅ Captured " + products.size() + " products:");
            for (int i = 0; i < products.size(); i++) {
                System.out.println("  " + (i + 1) + ". " + products.get(i));
            }
            
        } catch (Exception e) {
            System.err.println("❌ Example 1 failed: " + e.getMessage());
        }
    }

    /**
     * Example 2: Complete validation flow with your product structure
     */
    private static void example2_CompleteValidation() {
        System.out.println("\n📋 Example 2: Complete Validation Flow");
        System.out.println("-".repeat(40));
        
        try {
            // Create validator
            ProductValidator validator = new ProductValidator();
            
            // Run complete validation - this will automatically:
            // 1. Read expected products from Excel
            // 2. Apply filters using your dropdown structure
            // 3. Capture products using your product-card structure
            // 4. Compare and generate results
            ProductComparisonResult result = validator.validateProductsForCategory(
                "LLC", "Alaska", "Entity Formation", null
            );
            
            // Display results
            System.out.println("\\n" + result.getResultSummary());
            
        } catch (Exception e) {
            System.err.println("❌ Example 2 failed: " + e.getMessage());
        }
    }

    /**
     * Example 3: Using custom XPath specifically for your product structure
     */
    private static void example3_CustomXPathForYourStructure() {
        System.out.println("\n📋 Example 3: Custom XPath for Your Structure");
        System.out.println("-".repeat(40));
        
        try {
            ProductFilterPage filterPage = new ProductFilterPage(DriverManager.getDriver());
            
            // Set custom XPaths for your specific application
            // Update these XPaths to match your actual dropdown elements
            filterPage.setCustomXPath("category", "//your-category-dropdown-xpath");
            filterPage.setCustomXPath("entityType", "//your-entity-dropdown-xpath");
            filterPage.setCustomXPath("state", "//your-state-dropdown-xpath");
            
            // Apply filters
            filterPage.applyFilters("Entity Formation", "LLC", "Alaska", null);
            
            // Capture products using the exact XPath for your product structure
            String exactProductXPath = "//div[@class='product-card']//div[@class='product-name']";
            List<String> products = filterPage.captureProductsWithCustomXPath(exactProductXPath);
            
            System.out.println("✅ Captured " + products.size() + " products using exact XPath:");
            for (String product : products) {
                System.out.println("  • " + product);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Example 3 failed: " + e.getMessage());
        }
    }
}

/**
 * CONFIGURATION FOR YOUR APPLICATION:
 * 
 * 1. **Update Dropdown XPaths**: 
 *    Replace the default XPaths with your actual dropdown selectors:
 *    
 *    filterPage.setCustomXPath("category", "//your-actual-category-xpath");
 *    filterPage.setCustomXPath("entityType", "//your-actual-entity-xpath");
 *    filterPage.setCustomXPath("state", "//your-actual-state-xpath");
 * 
 * 2. **Product Capture**: 
 *    The system is now optimized for your product-card structure and will automatically:
 *    - Look for div[@class='product-card'] elements
 *    - Extract text from div[@class='product-name'] within each card
 *    - Filter out prices, buttons, and descriptions
 * 
 * 3. **Custom Product XPath** (if needed):
 *    If the automatic detection doesn't work, use:
 *    
 *    String productXPath = "//div[@class='product-card']//div[@class='product-name']";
 *    List<String> products = filterPage.captureProductsWithCustomXPath(productXPath);
 * 
 * 4. **Expected Results**:
 *    Based on your HTML, the system should capture product names like:
 *    - "ARTICLES OF ORGANIZATION"
 *    - And other product names from similar cards
 * 
 * 5. **Troubleshooting**:
 *    - Check console output for detailed logging
 *    - Verify dropdown XPaths are correct
 *    - Ensure product cards are visible after filter application
 *    - Use browser developer tools to verify XPaths
 */
