package models;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Model class to hold database validation test data
 */
public class DatabaseTestData {
    private String testCaseId;
    private String stepDescription;
    private String tableName;
    private List<String> columnsToCheck;
    private String whereCondition;
    private List<String> expectedValues;
    private String testData; // Original test data from Excel (for substitution)

    // Unique record identification using multiple fields
    private String uniqueIdentifierStrategy; // COMPOSITE, LATEST, TIMESTAMP, CUSTOM
    private String timestampColumn; // Column name for timestamp-based identification
    private String additionalFilters; // Additional WHERE conditions for precise identification
    private long testExecutionTimestamp; // When this test started (for LATEST strategy)
    private Map<String, String> compositeIdentifiers; // Multiple field-value pairs for unique identification

    // Results after validation
    private Map<String, Object> actualValues;
    private boolean validationPassed;
    private String validationMessage;
    private List<String> validationErrors;

    public DatabaseTestData() {
        this.columnsToCheck = new ArrayList<>();
        this.expectedValues = new ArrayList<>();
        this.actualValues = new HashMap<>();
        this.validationErrors = new ArrayList<>();
        this.compositeIdentifiers = new HashMap<>();
        this.validationPassed = false;
        this.testExecutionTimestamp = System.currentTimeMillis();
    }

    public DatabaseTestData(String testCaseId, String stepDescription) {
        this();
        this.testCaseId = testCaseId;
        this.stepDescription = stepDescription;
    }

    // Getters and Setters
    public String getTestCaseId() {
        return testCaseId;
    }

    public void setTestCaseId(String testCaseId) {
        this.testCaseId = testCaseId;
    }

    public String getStepDescription() {
        return stepDescription;
    }

    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getColumnsToCheck() {
        return columnsToCheck;
    }

    public void setColumnsToCheck(List<String> columnsToCheck) {
        this.columnsToCheck = columnsToCheck;
    }

    public void addColumnToCheck(String column) {
        if (column != null && !column.trim().isEmpty()) {
            this.columnsToCheck.add(column.trim());
        }
    }

    public String getWhereCondition() {
        return whereCondition;
    }

    public void setWhereCondition(String whereCondition) {
        this.whereCondition = whereCondition;
    }

    public List<String> getExpectedValues() {
        return expectedValues;
    }

    public void setExpectedValues(List<String> expectedValues) {
        this.expectedValues = expectedValues;
    }

    public void addExpectedValue(String value) {
        if (value != null && !value.trim().isEmpty()) {
            this.expectedValues.add(value.trim());
        }
    }

    public String getTestData() {
        return testData;
    }

    public void setTestData(String testData) {
        this.testData = testData;
    }

    public String getUniqueIdentifierStrategy() {
        return uniqueIdentifierStrategy;
    }

    public void setUniqueIdentifierStrategy(String uniqueIdentifierStrategy) {
        this.uniqueIdentifierStrategy = uniqueIdentifierStrategy;
    }

    public String getTimestampColumn() {
        return timestampColumn;
    }

    public void setTimestampColumn(String timestampColumn) {
        this.timestampColumn = timestampColumn;
    }

    public String getAdditionalFilters() {
        return additionalFilters;
    }

    public void setAdditionalFilters(String additionalFilters) {
        this.additionalFilters = additionalFilters;
    }

    public long getTestExecutionTimestamp() {
        return testExecutionTimestamp;
    }

    public void setTestExecutionTimestamp(long testExecutionTimestamp) {
        this.testExecutionTimestamp = testExecutionTimestamp;
    }

    public Map<String, String> getCompositeIdentifiers() {
        return compositeIdentifiers;
    }

    public void setCompositeIdentifiers(Map<String, String> compositeIdentifiers) {
        this.compositeIdentifiers = compositeIdentifiers;
    }

    public void addCompositeIdentifier(String column, String value) {
        if (column != null && !column.trim().isEmpty() && value != null) {
            this.compositeIdentifiers.put(column.trim(), value.trim());
        }
    }



    public Map<String, Object> getActualValues() {
        return actualValues;
    }

    public void setActualValues(Map<String, Object> actualValues) {
        this.actualValues = actualValues;
    }

    public void addActualValue(String column, Object value) {
        this.actualValues.put(column, value);
    }

    public boolean isValidationPassed() {
        return validationPassed;
    }

    public void setValidationPassed(boolean validationPassed) {
        this.validationPassed = validationPassed;
    }

    public String getValidationMessage() {
        return validationMessage;
    }

    public void setValidationMessage(String validationMessage) {
        this.validationMessage = validationMessage;
    }

    public List<String> getValidationErrors() {
        return validationErrors;
    }

    public void setValidationErrors(List<String> validationErrors) {
        this.validationErrors = validationErrors;
    }

    public void addValidationError(String error) {
        if (error != null && !error.trim().isEmpty()) {
            this.validationErrors.add(error.trim());
        }
    }

    /**
     * Get the WHERE condition with test data substituted
     */
    public String getProcessedWhereCondition() {
        if (whereCondition == null || testData == null) {
            return whereCondition;
        }

        // Replace {{TEST_DATA}} placeholder with actual test data
        return whereCondition.replace("{{TEST_DATA}}", testData)
                           .replace("{{TESTDATA}}", testData)
                           .replace("{{test_data}}", testData);
    }

    /**
     * Check if this step has database validation configured
     */
    public boolean hasDatabaseValidation() {
        return tableName != null && !tableName.trim().isEmpty()
               && !columnsToCheck.isEmpty()
               && whereCondition != null && !whereCondition.trim().isEmpty();
    }

    /**
     * Generate SQL query for validation with composite identifier support
     */
    public String generateValidationQuery() {
        if (!hasDatabaseValidation()) {
            return null;
        }

        StringBuilder query = new StringBuilder("SELECT ");

        // Add columns to select
        for (int i = 0; i < columnsToCheck.size(); i++) {
            if (i > 0) {
                query.append(", ");
            }
            query.append(columnsToCheck.get(i));
        }

        // Add timestamp column if using LATEST strategy
        if ("LATEST".equalsIgnoreCase(uniqueIdentifierStrategy) && timestampColumn != null && !timestampColumn.trim().isEmpty()) {
            query.append(", ").append(timestampColumn);
        }

        query.append(" FROM ").append(tableName);

        // Build WHERE clause with composite conditions
        StringBuilder whereClause = new StringBuilder();

        // Add base WHERE condition
        String processedCondition = getProcessedWhereCondition();
        if (processedCondition != null && !processedCondition.trim().isEmpty()) {
            whereClause.append(processedCondition);
        }

        // Add composite identifier conditions
        if (compositeIdentifiers != null && !compositeIdentifiers.isEmpty()) {
            for (Map.Entry<String, String> identifier : compositeIdentifiers.entrySet()) {
                if (whereClause.length() > 0) {
                    whereClause.append(" AND ");
                }
                String value = identifier.getValue().replace("{{TEST_DATA}}", testData != null ? testData : "");
                whereClause.append(identifier.getKey()).append("='").append(value).append("'");
            }
        }

        // Add additional filters
        if (additionalFilters != null && !additionalFilters.trim().isEmpty()) {
            if (whereClause.length() > 0) {
                whereClause.append(" AND ");
            }
            String processedFilters = additionalFilters.replace("{{TEST_DATA}}", testData != null ? testData : "");
            whereClause.append(processedFilters);
        }

        if (whereClause.length() > 0) {
            query.append(" WHERE ").append(whereClause);
        }

        // Add ORDER BY for LATEST strategy
        if ("LATEST".equalsIgnoreCase(uniqueIdentifierStrategy) && timestampColumn != null && !timestampColumn.trim().isEmpty()) {
            query.append(" ORDER BY ").append(timestampColumn).append(" DESC LIMIT 1");
        }

        return query.toString();
    }

    /**
     * Get validation summary
     */
    public String getValidationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("🔍 Database Validation Summary for ").append(testCaseId).append(":\n");
        summary.append("   Table: ").append(tableName).append("\n");
        summary.append("   Columns: ").append(String.join(", ", columnsToCheck)).append("\n");
        summary.append("   Where: ").append(getProcessedWhereCondition()).append("\n");
        summary.append("   Expected: ").append(String.join(", ", expectedValues)).append("\n");
        summary.append("   Actual: ").append(actualValues.toString()).append("\n");
        summary.append("   Result: ").append(validationPassed ? "✅ PASSED" : "❌ FAILED").append("\n");

        if (!validationErrors.isEmpty()) {
            summary.append("   Errors:\n");
            for (String error : validationErrors) {
                summary.append("      - ").append(error).append("\n");
            }
        }

        return summary.toString();
    }

    @Override
    public String toString() {
        return "DatabaseTestData{" +
                "testCaseId='" + testCaseId + '\'' +
                ", stepDescription='" + stepDescription + '\'' +
                ", tableName='" + tableName + '\'' +
                ", columnsToCheck=" + columnsToCheck +
                ", whereCondition='" + whereCondition + '\'' +
                ", expectedValues=" + expectedValues +
                ", testData='" + testData + '\'' +
                ", validationPassed=" + validationPassed +
                '}';
    }
}
