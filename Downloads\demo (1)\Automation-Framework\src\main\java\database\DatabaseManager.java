package database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import drivers.ConfigManager;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Database Connection Manager using HikariCP connection pooling
 * Supports multiple database types: MySQL, PostgreSQL, SQL Server, Oracle
 */
public class DatabaseManager {
    private static HikariDataSource dataSource;
    private static boolean initialized = false;

    /**
     * Initialize the database connection pool
     */
    public static synchronized void initialize() {
        if (initialized) {
            return;
        }

        try {
            HikariConfig config = new HikariConfig();
            
            // Get database configuration from config.properties
            String dbType = ConfigManager.get("db.type");
            String host = ConfigManager.get("db.host");
            String port = ConfigManager.get("db.port");
            String dbName = ConfigManager.get("db.name");
            String username = ConfigManager.get("db.username");
            String password = ConfigManager.get("db.password");
            
            // Build JDBC URL based on database type
            String jdbcUrl = buildJdbcUrl(dbType, host, port, dbName);
            
            config.setJdbcUrl(jdbcUrl);
            config.setUsername(username);
            config.setPassword(password);
            
            // Connection pool settings
            config.setMaximumPoolSize(Integer.parseInt(ConfigManager.get("db.maxPoolSize")));
            config.setConnectionTimeout(Long.parseLong(ConfigManager.get("db.connectionTimeout")));
            config.setValidationTimeout(5000);
            config.setLeakDetectionThreshold(60000);
            
            // Set validation query based on database type
            String validationQuery = getValidationQuery(dbType);
            config.setConnectionTestQuery(validationQuery);
            
            dataSource = new HikariDataSource(config);
            initialized = true;
            
            System.out.println("✅ Database connection pool initialized successfully");
            System.out.println("   Database Type: " + dbType.toUpperCase());
            System.out.println("   Host: " + host + ":" + port);
            System.out.println("   Database: " + dbName);
            System.out.println("   Max Pool Size: " + config.getMaximumPoolSize());
            
        } catch (Exception e) {
            System.err.println("❌ Failed to initialize database connection pool: " + e.getMessage());
            throw new RuntimeException("Database initialization failed", e);
        }
    }

    /**
     * Get a database connection from the pool
     */
    public static Connection getConnection() throws SQLException {
        if (!initialized) {
            initialize();
        }
        return dataSource.getConnection();
    }

    /**
     * Close the connection pool
     */
    public static synchronized void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            initialized = false;
            System.out.println("✅ Database connection pool closed");
        }
    }

    /**
     * Build JDBC URL based on database type
     */
    private static String buildJdbcUrl(String dbType, String host, String port, String dbName) {
        switch (dbType.toLowerCase()) {
            case "mysql":
                return String.format("**********************************************************************************", 
                                   host, port, dbName);
            case "postgresql":
                return String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
            case "sqlserver":
                return String.format("****************************************************", host, port, dbName);
            case "oracle":
                return String.format("**************************", host, port, dbName);
            default:
                throw new IllegalArgumentException("Unsupported database type: " + dbType);
        }
    }

    /**
     * Get validation query based on database type
     */
    private static String getValidationQuery(String dbType) {
        switch (dbType.toLowerCase()) {
            case "mysql":
            case "postgresql":
                return "SELECT 1";
            case "sqlserver":
                return "SELECT 1";
            case "oracle":
                return "SELECT 1 FROM DUAL";
            default:
                return "SELECT 1";
        }
    }

    /**
     * Test database connectivity
     */
    public static boolean testConnection() {
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            System.err.println("❌ Database connection test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get database type from configuration
     */
    public static String getDatabaseType() {
        return ConfigManager.get("db.type");
    }
}
