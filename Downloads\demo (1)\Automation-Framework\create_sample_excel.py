#!/usr/bin/env python3
"""
Simple Python script to create sample Excel files for testing
Run this script to create LLC.xlsx and Corp.xlsx with sample data
"""

import pandas as pd
import os

def create_sample_excel_files():
    # Create the testdata directory if it doesn't exist
    testdata_dir = "src/resources/testdata"
    os.makedirs(testdata_dir, exist_ok=True)
    
    # Sample states
    states = ["Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia"]
    
    # Create LLC.xlsx
    create_llc_file(testdata_dir, states)
    
    # Create Corp.xlsx
    create_corp_file(testdata_dir, states)
    
    print("✅ Sample Excel files created successfully!")
    print("📁 Files created:")
    print(f"   - {testdata_dir}/LLC.xlsx")
    print(f"   - {testdata_dir}/Corp.xlsx")

def create_llc_file(testdata_dir, states):
    # Entity Formation sheet data
    entity_formation_data = {
        "Product Name": [
            "Basic LLC Formation",
            "Expedited LLC Formation", 
            "Registered Agent Service",
            "EIN Service",
            "Operating Agreement"
        ]
    }
    
    # Add state columns with sample data
    for state in states:
        if state == "Arizona":  # Make Arizona have some missing products
            entity_formation_data[state] = [
                "Basic LLC Formation",
                "",  # Missing expedited
                "Registered Agent Service", 
                "EIN Service",
                ""   # Missing operating agreement
            ]
        else:
            entity_formation_data[state] = [
                "Basic LLC Formation",
                "Expedited LLC Formation",
                "Registered Agent Service",
                "EIN Service", 
                "Operating Agreement"
            ]
    
    # Compliance sheet data
    compliance_data = {
        "Product Name": [
            "Annual Report Filing",
            "State Tax Registration",
            "Business License Research", 
            "Compliance Calendar"
        ]
    }
    
    for state in states:
        if state == "Alaska":  # Make Alaska have some missing compliance products
            compliance_data[state] = [
                "Annual Report Filing",
                "",  # Missing tax registration
                "Business License Research",
                "Compliance Calendar"
            ]
        else:
            compliance_data[state] = [
                "Annual Report Filing", 
                "State Tax Registration",
                "Business License Research",
                "Compliance Calendar"
            ]
    
    # Create Excel file with multiple sheets
    with pd.ExcelWriter(f"{testdata_dir}/LLC.xlsx", engine='openpyxl') as writer:
        pd.DataFrame(entity_formation_data).to_excel(writer, sheet_name='Entity Formation', index=False)
        pd.DataFrame(compliance_data).to_excel(writer, sheet_name='Compliance', index=False)

def create_corp_file(testdata_dir, states):
    # Entity Formation sheet data
    entity_formation_data = {
        "Product Name": [
            "Basic Corporation Formation",
            "Expedited Corporation Formation",
            "Registered Agent Service", 
            "EIN Service",
            "Corporate Bylaws"
        ]
    }
    
    # Add state columns with sample data
    for state in states:
        if state == "Arizona":  # Make Arizona have some missing products
            entity_formation_data[state] = [
                "Basic Corporation Formation",
                "",  # Missing expedited
                "Registered Agent Service",
                "EIN Service", 
                ""   # Missing bylaws
            ]
        else:
            entity_formation_data[state] = [
                "Basic Corporation Formation",
                "Expedited Corporation Formation", 
                "Registered Agent Service",
                "EIN Service",
                "Corporate Bylaws"
            ]
    
    # Compliance sheet data  
    compliance_data = {
        "Product Name": [
            "Annual Report Filing",
            "State Tax Registration",
            "Business License Research",
            "Compliance Calendar"
        ]
    }
    
    for state in states:
        if state == "Alaska":  # Make Alaska have some missing compliance products
            compliance_data[state] = [
                "Annual Report Filing",
                "",  # Missing tax registration
                "Business License Research", 
                "Compliance Calendar"
            ]
        else:
            compliance_data[state] = [
                "Annual Report Filing",
                "State Tax Registration", 
                "Business License Research",
                "Compliance Calendar"
            ]
    
    # Create Excel file with multiple sheets
    with pd.ExcelWriter(f"{testdata_dir}/Corp.xlsx", engine='openpyxl') as writer:
        pd.DataFrame(entity_formation_data).to_excel(writer, sheet_name='Entity Formation', index=False)
        pd.DataFrame(compliance_data).to_excel(writer, sheet_name='Compliance', index=False)

if __name__ == "__main__":
    try:
        create_sample_excel_files()
    except ImportError:
        print("❌ pandas and openpyxl are required to run this script")
        print("Install them with: pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ Error creating Excel files: {e}")
