package com.automation.framework.core.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * Centralized configuration manager for the automation framework
 * Handles all configuration properties including database, Excel, and UI settings
 */
public class FrameworkConfig {
    private static Properties props;
    private static FrameworkConfig instance;

    private FrameworkConfig() {
        loadProperties();
    }

    public static synchronized FrameworkConfig getInstance() {
        if (instance == null) {
            instance = new FrameworkConfig();
        }
        return instance;
    }

    private void loadProperties() {
        props = new Properties();
        try {
            props.load(new FileInputStream("config.properties"));
        } catch (IOException e) {
            throw new RuntimeException("Could not load config.properties", e);
        }
    }

    // General configuration
    public String getBaseUrl() {
        return props.getProperty("baseUrl");
    }

    public String getBrowser() {
        return props.getProperty("browser", "chrome");
    }

    // Excel configuration
    public String getExcelFilePath() {
        return props.getProperty("excel.filePath", "src/resources/testdata/FieldInputSheet.xlsx");
    }

    public String getExcelSheetName() {
        return props.getProperty("excel.sheetName", "Sheet1");
    }

    public int getXPathColumn() {
        return Integer.parseInt(props.getProperty("excel.xpathColumn", "13"));
    }

    public int getDataColumn() {
        return Integer.parseInt(props.getProperty("excel.dataColumn", "9"));
    }

    // Database configuration
    public String getDatabaseType() {
        return props.getProperty("db.type", "mysql");
    }

    public String getDatabaseHost() {
        return props.getProperty("db.host", "localhost");
    }

    public String getDatabasePort() {
        return props.getProperty("db.port", "3306");
    }

    public String getDatabaseName() {
        return props.getProperty("db.name");
    }

    public String getDatabaseUsername() {
        return props.getProperty("db.username");
    }

    public String getDatabasePassword() {
        return props.getProperty("db.password");
    }

    public int getDatabaseMaxPoolSize() {
        return Integer.parseInt(props.getProperty("db.maxPoolSize", "10"));
    }

    public long getDatabaseConnectionTimeout() {
        return Long.parseLong(props.getProperty("db.connectionTimeout", "30000"));
    }

    public String getDatabaseValidationQuery() {
        return props.getProperty("db.validationQuery", "SELECT 1");
    }

    // Framework settings
    public int getImplicitWaitTimeout() {
        return Integer.parseInt(props.getProperty("framework.implicitWait", "10"));
    }

    public int getExplicitWaitTimeout() {
        return Integer.parseInt(props.getProperty("framework.explicitWait", "30"));
    }

    public boolean isHeadlessMode() {
        return Boolean.parseBoolean(props.getProperty("framework.headless", "false"));
    }

    public boolean isScreenshotOnFailure() {
        return Boolean.parseBoolean(props.getProperty("framework.screenshotOnFailure", "true"));
    }

    // Reporting settings
    public String getReportPath() {
        return props.getProperty("report.path", "test-output/reports");
    }

    public String getReportFormat() {
        return props.getProperty("report.format", "html");
    }

    // Validation settings
    public int getValidationRetryCount() {
        return Integer.parseInt(props.getProperty("validation.retryCount", "3"));
    }

    public int getValidationRetryDelay() {
        return Integer.parseInt(props.getProperty("validation.retryDelay", "1000"));
    }

    // Generic property getter
    public String getProperty(String key) {
        return props.getProperty(key);
    }

    public String getProperty(String key, String defaultValue) {
        return props.getProperty(key, defaultValue);
    }

    public Properties getAllProperties() {
        return new Properties(props);
    }

    /**
     * Reload configuration from file
     */
    public void reloadConfiguration() {
        loadProperties();
    }

    /**
     * Print all configuration for debugging
     */
    public void printConfiguration() {
        System.out.println("🔧 Framework Configuration:");
        System.out.println("   Base URL: " + getBaseUrl());
        System.out.println("   Browser: " + getBrowser());
        System.out.println("   Excel File: " + getExcelFilePath());
        System.out.println("   Excel Sheet: " + getExcelSheetName());
        System.out.println("   Database Type: " + getDatabaseType());
        System.out.println("   Database Host: " + getDatabaseHost() + ":" + getDatabasePort());
        System.out.println("   Database Name: " + getDatabaseName());
        System.out.println("   Report Path: " + getReportPath());
        System.out.println("   Headless Mode: " + isHeadlessMode());
        System.out.println("   Screenshot on Failure: " + isScreenshotOnFailure());
    }
}
