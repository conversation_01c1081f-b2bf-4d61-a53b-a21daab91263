package pages;

import drivers.DriverManager;
import models.ProductComparisonResult;
import models.TestData;
import org.openqa.selenium.WebDriver;
import utils.ProductDataReader;
// import utiles.ExcelUtils; // Not needed for current implementation

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Main class for validating products between Excel expected data and UI actual data
 */
public class ProductValidator {
    private final WebDriver driver;
    private final ProductFilterPage filterPage;

    public ProductValidator() {
        this.driver = DriverManager.getDriver();
        this.filterPage = new ProductFilterPage(driver);
    }

    /**
     * Main method to perform dynamic product validation
     * This is the entry point called from test runners
     *
     * @param state State name
     * @param entityType Entity type (LLC/Corp)
     */
    public void dynamicOrder(String state, String entityType) {
        try {
            System.out.println("🚀 Starting Product Validation Flow");
            System.out.println("Entity Type: " + entityType + ", State: " + state);

            // Get available categories for the entity type
            List<String> categories = ProductDataReader.getAvailableCategories(entityType);

            if (categories.isEmpty()) {
                System.err.println("❌ No categories found for entity type: " + entityType);
                return;
            }

            System.out.println("📋 Found categories: " + categories);

            // Test each category
            for (String category : categories) {
                System.out.println("\n" + "=".repeat(50));
                System.out.println("🔍 Testing Category: " + category);
                System.out.println("=".repeat(50));

                validateProductsForCategory(entityType, state, category, null);
            }

            System.out.println("\n✅ Product Validation Flow Completed");

        } catch (Exception e) {
            System.err.println("❌ Error in dynamic order validation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Validates products for a specific category
     */
    public ProductComparisonResult validateProductsForCategory(String entityType, String state, String category, String county) {
        ProductComparisonResult result = new ProductComparisonResult(entityType, state, category);
        result.setCounty(county);

        try {
            // Step 1: Read expected data from Excel
            System.out.println("📖 Step 1: Reading expected data from Excel...");
            TestData expectedData = ProductDataReader.readExpectedProducts(entityType, state, category);
            result.setExpectedProducts(expectedData.getExpectedProducts());

            if (expectedData.getExpectedProducts().isEmpty()) {
                System.out.println("⚠️ No expected products found for " + entityType + " - " + state + " - " + category);
                result.setTestPassed(false);
                result.setResultSummary("No expected products found in Excel");
                return result;
            }

            // Step 2: Apply filters on UI
            System.out.println("🎛️ Step 2: Applying filters on UI...");
            filterPage.waitForPageLoad();
            try {
                filterPage.applyFilters(category, entityType, state, county);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Filter application interrupted", e);
            }

            // Step 3: Capture actual products from UI
            System.out.println("📱 Step 3: Capturing actual products from UI...");
            List<String> actualProducts = filterPage.captureActualProducts();
            result.setActualProducts(actualProducts);

            // Step 4: Perform comparison
            System.out.println("⚖️ Step 4: Performing comparison...");
            result.performComparison();

            // Step 5: Log results
            System.out.println("📝 Step 5: Logging results...");
            logResults(result);

            // Step 6: Write results to Excel (optional)
            writeResultsToExcel(result);

            // Step 7: Take screenshot for debugging
            filterPage.takeScreenshot("validation_" + entityType + "_" + state + "_" + category);

        } catch (Exception e) {
            System.err.println("❌ Error validating products for category " + category + ": " + e.getMessage());
            e.printStackTrace();
            result.setTestPassed(false);
            result.setResultSummary("Error during validation: " + e.getMessage());
        }

        return result;
    }

    /**
     * Logs the comparison results to console and file
     */
    private void logResults(ProductComparisonResult result) {
        // Log to console
        System.out.println("\n" + result.getResultSummary());

        // Log to file
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            String fileName = "test-results/product_validation_" + timestamp + ".log";

            // Create directory if it doesn't exist
            java.io.File directory = new java.io.File("test-results");
            if (!directory.exists()) {
                directory.mkdirs();
            }

            try (FileWriter writer = new FileWriter(fileName, true)) {
                writer.write("\n" + "=".repeat(80) + "\n");
                writer.write("Timestamp: " + LocalDateTime.now() + "\n");
                writer.write(result.getResultSummary());
                writer.write("\n" + "=".repeat(80) + "\n");
            }

            System.out.println("📄 Results logged to file: " + fileName);

        } catch (IOException e) {
            System.err.println("❌ Failed to write results to file: " + e.getMessage());
        }
    }

    /**
     * Writes results back to Excel file for tracking
     */
    private void writeResultsToExcel(ProductComparisonResult result) {
        try {
            String resultFilePath = "src/resources/testdata/validation_results.xlsx";

            // This is a simplified implementation
            // In a real scenario, you might want to create a more structured Excel report

            System.out.println("📊 Results could be written to Excel: " + resultFilePath);
            // Implementation would depend on your specific Excel structure requirements

        } catch (Exception e) {
            System.err.println("❌ Failed to write results to Excel: " + e.getMessage());
        }
    }

    /**
     * Validates products with custom parameters (for advanced testing)
     */
    public ProductComparisonResult validateProducts(String entityType, String state, String category, String county,
                                                   List<String> customExpectedProducts) {
        ProductComparisonResult result = new ProductComparisonResult(entityType, state, category);
        result.setCounty(county);

        if (customExpectedProducts != null && !customExpectedProducts.isEmpty()) {
            result.setExpectedProducts(customExpectedProducts);
        } else {
            // Read from Excel
            TestData expectedData = ProductDataReader.readExpectedProducts(entityType, state, category);
            result.setExpectedProducts(expectedData.getExpectedProducts());
        }

        try {
            // Apply filters and capture products
            try {
                filterPage.applyFilters(category, entityType, state, county);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Filter application interrupted", e);
            }
            List<String> actualProducts = filterPage.captureActualProducts();
            result.setActualProducts(actualProducts);

            // Perform comparison
            result.performComparison();

            // Log results
            logResults(result);

        } catch (Exception e) {
            System.err.println("❌ Error in custom validation: " + e.getMessage());
            result.setTestPassed(false);
            result.setResultSummary("Error during validation: " + e.getMessage());
        }

        return result;
    }

    /**
     * Quick validation method for single category testing
     */
    public boolean quickValidate(String entityType, String state, String category) {
        ProductComparisonResult result = validateProductsForCategory(entityType, state, category, null);
        return result.isTestPassed();
    }

    /**
     * Batch validation for multiple states
     */
    public void validateMultipleStates(String entityType, String category, List<String> states) {
        System.out.println("🔄 Starting batch validation for " + states.size() + " states");

        int passedTests = 0;
        int totalTests = states.size();

        for (String state : states) {
            System.out.println("\n" + "-".repeat(40));
            System.out.println("Testing State: " + state);
            System.out.println("-".repeat(40));

            ProductComparisonResult result = validateProductsForCategory(entityType, state, category, null);
            if (result.isTestPassed()) {
                passedTests++;
            }
        }

        System.out.println("\n" + "=".repeat(50));
        System.out.println("BATCH VALIDATION SUMMARY");
        System.out.println("=".repeat(50));
        System.out.println("Total Tests: " + totalTests);
        System.out.println("Passed: " + passedTests);
        System.out.println("Failed: " + (totalTests - passedTests));
        System.out.println("Success Rate: " + (passedTests * 100.0 / totalTests) + "%");
        System.out.println("=".repeat(50));
    }
}
