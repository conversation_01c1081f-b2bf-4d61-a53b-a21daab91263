package utils;

import org.openqa.selenium.WebDriver;
import utiles.ExcelUtils;
import utiles.SmartActionUtils;
import drivers.DriverManager;
import config.ExcelConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * Dynamic Test Case Executor that reads test cases from Excel and executes them
 */
public class TestCaseExecutor {

    private final WebDriver driver;
    private final SmartActionUtils smartActionUtils;
    private final ExcelUtils excelUtils;
    private final String excelFilePath;
    private final String sheetName;

    // Column indices - now configurable via ExcelConfig class
    private static final int TEST_CASE_ID_COLUMN = ExcelConfig.TEST_CASE_ID_COLUMN;
    private static final int STEP_DESCRIPTION_COLUMN = ExcelConfig.STEP_DESCRIPTION_COLUMN;
    private static final int TEST_DATA_COLUMN = ExcelConfig.TEST_DATA_COLUMN;
    private static final int XPATH_COLUMN = ExcelConfig.XPATH_COLUMN;

    public TestCaseExecutor() {
        this.driver = DriverManager.getDriver();
        this.smartActionUtils = new SmartActionUtils(driver);
        this.excelUtils = new ExcelUtils();
        this.excelFilePath = ExcelConfig.EXCEL_FILE_PATH;
        this.sheetName = detectCorrectSheetName();

        System.out.println("📄 TestCaseExecutor initialized:");
        System.out.println("   File: " + excelFilePath);
        System.out.println("   Sheet: " + sheetName);

        // Print configuration for debugging
        ExcelConfig.printConfiguration();
    }

    /**
     * Detects the correct sheet name by trying common variations
     */
    private String detectCorrectSheetName() {
        // Try the configured sheet name first
        try {
            excelUtils.getCellData(excelFilePath, ExcelConfig.SHEET_NAME, 1, 1);
            System.out.println("✅ Found configured sheet: " + ExcelConfig.SHEET_NAME);
            return ExcelConfig.SHEET_NAME;
        } catch (Exception e) {
            System.out.println("⚠️ Configured sheet '" + ExcelConfig.SHEET_NAME + "' not found, trying alternatives...");
        }

        // Try alternative sheet names
        for (String sheetName : ExcelConfig.POSSIBLE_SHEET_NAMES) {
            try {
                excelUtils.getCellData(excelFilePath, sheetName, 1, 1);
                System.out.println("✅ Found sheet: " + sheetName);
                return sheetName;
            } catch (Exception e) {
                // Continue trying
            }
        }

        System.out.println("⚠️ Using default sheet name: " + ExcelConfig.SHEET_NAME);
        return ExcelConfig.SHEET_NAME; // Default fallback
    }

    /**
     * Executes a specific test case by ID (e.g., "TC001", "TC002")
     * @param testCaseId The test case ID to execute
     * @return true if test case executed successfully, false otherwise
     */
    public boolean executeTestCase(String testCaseId) {
        try {
            System.out.println("🚀 Executing Test Case: " + testCaseId);

            // Find all rows for this test case
            List<TestStep> testSteps = getTestStepsForTestCase(testCaseId);

            if (testSteps.isEmpty()) {
                System.err.println("❌ No test steps found for test case: " + testCaseId);
                return false;
            }

            System.out.println("📋 Found " + testSteps.size() + " steps for " + testCaseId);

            // Execute each step
            for (int i = 0; i < testSteps.size(); i++) {
                TestStep step = testSteps.get(i);
                System.out.println("🔄 Executing Step " + (i + 1) + ": " + step.getDescription());

                boolean stepResult = executeTestStep(step);
                if (!stepResult) {
                    System.err.println("❌ Step " + (i + 1) + " failed: " + step.getDescription());
                    return false;
                }

                System.out.println("✅ Step " + (i + 1) + " completed successfully");
                Thread.sleep(1000); // Wait between steps
            }

            System.out.println("✅ Test Case " + testCaseId + " executed successfully!");
            return true;

        } catch (Exception e) {
            System.err.println("❌ Error executing test case " + testCaseId + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Reads all test steps for a specific test case from Excel
     */
    private List<TestStep> getTestStepsForTestCase(String testCaseId) {
        List<TestStep> testSteps = new ArrayList<>();

        try {
            System.out.println("🔍 Searching for test case: " + testCaseId);
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);
            System.out.println("📊 Total rows in Excel: " + totalRows);

            boolean foundTestCaseId = false;
            int maxRowsToCheck = Math.min(totalRows, 50); // Check first 50 rows

            for (int rowNum = 1; rowNum <= maxRowsToCheck; rowNum++) {
                try {
                    String currentTestCaseId = excelUtils.getCellData(excelFilePath, sheetName, rowNum, TEST_CASE_ID_COLUMN);

                    // Debug: Show what we're finding in the first few rows
                    if (rowNum <= 10) {
                        System.out.println("🔍 Row " + rowNum + ", Column A: '" + currentTestCaseId + "'");
                    }

                    if (currentTestCaseId != null && testCaseId.equalsIgnoreCase(currentTestCaseId.trim())) {
                        foundTestCaseId = true;

                        String description = excelUtils.getCellData(excelFilePath, sheetName, rowNum, STEP_DESCRIPTION_COLUMN);
                        String testData = excelUtils.getCellData(excelFilePath, sheetName, rowNum, TEST_DATA_COLUMN);
                        String xpath = excelUtils.getCellData(excelFilePath, sheetName, rowNum, XPATH_COLUMN);

                        System.out.println("✅ Found " + testCaseId + " at row " + rowNum + ":");
                        System.out.println("   Description: " + description);
                        System.out.println("   Test Data: " + testData);
                        System.out.println("   XPath: " + xpath);

                        // Only add steps that have an XPath (actual action steps)
                        if (xpath != null && !xpath.trim().isEmpty()) {
                            TestStep step = new TestStep(rowNum, description, testData, xpath);
                            testSteps.add(step);
                            System.out.println("✅ Added step " + testSteps.size() + " for " + testCaseId);
                        } else {
                            System.out.println("⚠️ Skipping row " + rowNum + " - no XPath found");
                        }
                    }
                } catch (Exception e) {
                    System.err.println("❌ Error reading row " + rowNum + ": " + e.getMessage());
                }
            }

            if (!foundTestCaseId) {
                System.err.println("❌ Test case '" + testCaseId + "' not found in Column A (index " + TEST_CASE_ID_COLUMN + ")");
                System.out.println("🔧 Trying to find '" + testCaseId + "' in other columns...");
                searchInOtherColumns(testCaseId, maxRowsToCheck);
            }

        } catch (Exception e) {
            System.err.println("❌ Error reading test steps: " + e.getMessage());
            e.printStackTrace();
        }

        return testSteps;
    }

    /**
     * Searches for test case ID in other columns if not found in Column A
     */
    private void searchInOtherColumns(String testCaseId, int maxRows) {
        try {
            for (int rowNum = 1; rowNum <= Math.min(maxRows, 10); rowNum++) {
                for (int colNum = 1; colNum <= 15; colNum++) {
                    try {
                        String cellValue = excelUtils.getCellData(excelFilePath, sheetName, rowNum, colNum);
                        if (cellValue != null && cellValue.trim().equalsIgnoreCase(testCaseId)) {
                            System.out.println("🎯 Found '" + testCaseId + "' at Row " + rowNum + ", Column " + colNum + " (" + getColumnLetter(colNum) + ")");
                            System.out.println("🔧 You may need to update TEST_CASE_ID_COLUMN to " + colNum);
                        }
                    } catch (Exception e) {
                        // Continue searching
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error searching other columns: " + e.getMessage());
        }
    }

    /**
     * Converts column number to letter (1=A, 2=B, etc.)
     */
    private String getColumnLetter(int columnNumber) {
        String[] columns = {"", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O"};
        return columnNumber < columns.length ? columns[columnNumber] : "Col" + columnNumber;
    }

    /**
     * Executes a single test step
     */
    private boolean executeTestStep(TestStep step) {
        try {
            // Use SmartActionUtils to perform the action
            if (step.getTestData() != null && !step.getTestData().trim().isEmpty()) {
                // Step has test data - use it
                smartActionUtils.performSmartActionWithRetry(step.getRowNumber());
            } else {
                // Step has no test data - just perform the action (like clicking a button)
                smartActionUtils.performSmartActionWithRetry(step.getRowNumber());
            }

            return true;

        } catch (Exception e) {
            System.err.println("❌ Error executing step: " + e.getMessage());
            return false;
        }
    }

    /**
     * Gets all available test case IDs from the Excel file
     */
    public List<String> getAvailableTestCases() {
        List<String> testCaseIds = new ArrayList<>();

        try {
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);

            for (int rowNum = 1; rowNum <= totalRows; rowNum++) {
                String testCaseId = excelUtils.getCellData(excelFilePath, sheetName, rowNum, TEST_CASE_ID_COLUMN);

                if (testCaseId != null && !testCaseId.trim().isEmpty() &&
                    testCaseId.startsWith("TC") && !testCaseIds.contains(testCaseId.trim())) {
                    testCaseIds.add(testCaseId.trim());
                }
            }

        } catch (Exception e) {
            System.err.println("❌ Error reading test case IDs: " + e.getMessage());
        }

        return testCaseIds;
    }

    /**
     * Executes multiple test cases in sequence
     */
    public boolean executeMultipleTestCases(String... testCaseIds) {
        boolean allPassed = true;

        for (String testCaseId : testCaseIds) {
            System.out.println("\n" + "=".repeat(50));
            boolean result = executeTestCase(testCaseId);
            if (!result) {
                allPassed = false;
            }
            System.out.println("=".repeat(50));
        }

        return allPassed;
    }

    /**
     * Inner class to represent a test step
     */
    private static class TestStep {
        private final int rowNumber;
        private final String description;
        private final String testData;
        private final String xpath;

        public TestStep(int rowNumber, String description, String testData, String xpath) {
            this.rowNumber = rowNumber;
            this.description = description;
            this.testData = testData;
            this.xpath = xpath;
        }

        public int getRowNumber() { return rowNumber; }
        public String getDescription() { return description; }
        public String getTestData() { return testData; }
        public String getXpath() { return xpath; }

        @Override
        public String toString() {
            return "TestStep{" +
                    "rowNumber=" + rowNumber +
                    ", description='" + description + '\'' +
                    ", testData='" + testData + '\'' +
                    ", xpath='" + xpath + '\'' +
                    '}';
        }
    }
}
