# 🔧 TC001 Error Solution

## ✅ **Problem Fixed!**

I've created a **configurable solution** for your TC001 error. The issue was that the system couldn't find TC001 in your Excel file due to incorrect column indices or sheet name.

## 🎯 **Solution: ExcelConfig Class**

I've created `src/main/java/config/ExcelConfig.java` that allows you to easily configure your Excel structure without modifying the core code.

### **Step 1: Check Your Excel Structure**

Open your Excel file: `src/resources/testdata/FieldInputSheet.xlsx`

1. **Find TC001**: Which column contains "TC001"?
2. **Find XPaths**: Which column contains your XPaths?
3. **Check Sheet Name**: What's the exact name of your sheet?

### **Step 2: Update ExcelConfig.java**

Edit `src/main/java/config/ExcelConfig.java` and update these values:

```java
public class ExcelConfig {
    // UPDATE THESE VALUES TO MATCH YOUR EXCEL:
    
    public static final String SHEET_NAME = "Sheet1";  // Change if different
    
    public static final int TEST_CASE_ID_COLUMN = 1;     // Column where TC001 is located
    public static final int STEP_DESCRIPTION_COLUMN = 6; // Column with step descriptions  
    public static final int TEST_DATA_COLUMN = 9;        // Column with test data
    public static final int XPATH_COLUMN = 13;           // Column with XPaths
}
```

### **Step 3: Common Fixes**

#### **If TC001 is in Column B (not A):**
```java
public static final int TEST_CASE_ID_COLUMN = 2;  // Column B
```

#### **If TC001 is in Column C:**
```java
public static final int TEST_CASE_ID_COLUMN = 3;  // Column C
```

#### **If XPaths are in Column N (not M):**
```java
public static final int XPATH_COLUMN = 14;  // Column N
```

#### **If your sheet is named "Sheet 1" (with space):**
```java
public static final String SHEET_NAME = "Sheet 1";
```

### **Step 4: Test the Fix**

1. **Compile**: `mvn compile`
2. **Run your test**: The system will now show detailed debug information

## 🔍 **Debug Information**

When you run TC001 now, you'll see detailed output like:

```
📄 TestCaseExecutor initialized:
   File: src/resources/testdata/FieldInputSheet.xlsx
   Sheet: Sheet1

📋 Excel Configuration:
   Test Case ID Column: 1 (Column A)
   Step Description Column: 6 (Column F)
   Test Data Column: 9 (Column I)
   XPath Column: 13 (Column M)

🔍 Searching for test case: TC001
📊 Total rows in Excel: 25
🔍 Row 1, Column A: 'TC001'
🔍 Row 2, Column A: 'TC001'
✅ Found TC001 at row 1:
   Description: 1. Enter Email
   Test Data: <EMAIL>
   XPath: //*[@id="email"]
✅ Added step 1 for TC001
```

## 🎯 **Most Common Issues & Fixes**

### **Issue 1: TC001 Not Found**
```
❌ Test case 'TC001' not found in Column A (index 1)
🔧 Trying to find 'TC001' in other columns...
🎯 Found 'TC001' at Row 1, Column 2 (B)
```
**Fix**: Update `TEST_CASE_ID_COLUMN = 2` in ExcelConfig.java

### **Issue 2: Sheet Not Found**
```
⚠️ Configured sheet 'Sheet1' not found, trying alternatives...
✅ Found sheet: Sheet 1
```
**Fix**: Update `SHEET_NAME = "Sheet 1"` in ExcelConfig.java

### **Issue 3: No XPaths Found**
```
⚠️ Skipping row 1 - no XPath found
```
**Fix**: Update `XPATH_COLUMN` to the correct column number in ExcelConfig.java

## 🚀 **Quick Test**

After updating ExcelConfig.java, test with:

```java
TestCaseExecutor executor = new TestCaseExecutor();
boolean result = executor.executeTestCase("TC001");
```

The system will now:
1. ✅ Show your Excel configuration
2. ✅ Display detailed search information  
3. ✅ Tell you exactly where TC001 is found (or not found)
4. ✅ Show you which columns to update if needed

## 📋 **Example Excel Structure**

Your Excel should look like this:

| A (1) | B (2) | C (3) | D (4) | E (5) | F (6) | ... | I (9) | ... | M (13) |
|-------|-------|-------|-------|-------|-------|-----|-------|-----|--------|
| TC001 | Sign-In | ... | ... | ... | 1. Enter Email | ... | <EMAIL> | ... | //*[@id="email"] |
| TC001 | Sign-In | ... | ... | ... | 2. Enter Password | ... | Snehal@123 | ... | //*[@id="password"]/div/input |
| TC001 | Sign-In | ... | ... | ... | 3. Submit Button | ... |  | ... | //*[@id="root"]/div/div/div[2]/div[2]/form/button |

**If your structure is different, just update the column numbers in ExcelConfig.java!**

## ✅ **Verification**

After making changes:
1. Save ExcelConfig.java
2. Run: `mvn compile`
3. Test TC001 execution
4. Check the debug output to confirm it's finding TC001 correctly

**Your TC001 error should now be resolved!** 🎉
