package models;

import java.util.List;
import java.util.ArrayList;

/**
 * Model class to hold test data read from Excel files
 */
public class TestData {
    private String entityType;
    private String state;
    private String category;
    private String county;
    private List<String> expectedProducts;

    public TestData() {
        this.expectedProducts = new ArrayList<>();
    }

    public TestData(String entityType, String state, String category) {
        this();
        this.entityType = entityType;
        this.state = state;
        this.category = category;
    }

    // Getters and Setters
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public List<String> getExpectedProducts() {
        return expectedProducts;
    }

    public void setExpectedProducts(List<String> expectedProducts) {
        this.expectedProducts = expectedProducts;
    }

    public void addExpectedProduct(String product) {
        if (product != null && !product.trim().isEmpty()) {
            this.expectedProducts.add(product.trim());
        }
    }

    @Override
    public String toString() {
        return "TestData{" +
                "entityType='" + entityType + '\'' +
                ", state='" + state + '\'' +
                ", category='" + category + '\'' +
                ", county='" + county + '\'' +
                ", expectedProducts=" + expectedProducts +
                '}';
    }
}
