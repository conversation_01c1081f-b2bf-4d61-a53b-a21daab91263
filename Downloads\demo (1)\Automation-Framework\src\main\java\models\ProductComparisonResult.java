package models;

import java.util.List;
import java.util.ArrayList;

/**
 * Model class to hold the results of product comparison between Excel expected data and UI actual data
 */
public class ProductComparisonResult {
    private String entityType;
    private String state;
    private String category;
    private String county;
    private List<String> expectedProducts;
    private List<String> actualProducts;
    private List<String> matchedProducts;
    private List<String> missingProducts;
    private List<String> extraProducts;
    private boolean testPassed;
    private String resultSummary;

    public ProductComparisonResult() {
        this.expectedProducts = new ArrayList<>();
        this.actualProducts = new ArrayList<>();
        this.matchedProducts = new ArrayList<>();
        this.missingProducts = new ArrayList<>();
        this.extraProducts = new ArrayList<>();
    }

    public ProductComparisonResult(String entityType, String state, String category) {
        this();
        this.entityType = entityType;
        this.state = state;
        this.category = category;
    }

    // Getters and Setters
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public List<String> getExpectedProducts() {
        return expectedProducts;
    }

    public void setExpectedProducts(List<String> expectedProducts) {
        this.expectedProducts = expectedProducts;
    }

    public List<String> getActualProducts() {
        return actualProducts;
    }

    public void setActualProducts(List<String> actualProducts) {
        this.actualProducts = actualProducts;
    }

    public List<String> getMatchedProducts() {
        return matchedProducts;
    }

    public void setMatchedProducts(List<String> matchedProducts) {
        this.matchedProducts = matchedProducts;
    }

    public List<String> getMissingProducts() {
        return missingProducts;
    }

    public void setMissingProducts(List<String> missingProducts) {
        this.missingProducts = missingProducts;
    }

    public List<String> getExtraProducts() {
        return extraProducts;
    }

    public void setExtraProducts(List<String> extraProducts) {
        this.extraProducts = extraProducts;
    }

    public boolean isTestPassed() {
        return testPassed;
    }

    public void setTestPassed(boolean testPassed) {
        this.testPassed = testPassed;
    }

    public String getResultSummary() {
        return resultSummary;
    }

    public void setResultSummary(String resultSummary) {
        this.resultSummary = resultSummary;
    }

    /**
     * Performs the comparison logic and populates matched, missing, and extra products
     */
    public void performComparison() {
        // Clear previous results
        matchedProducts.clear();
        missingProducts.clear();
        extraProducts.clear();

        // Find matched products
        for (String expected : expectedProducts) {
            if (actualProducts.contains(expected)) {
                matchedProducts.add(expected);
            } else {
                missingProducts.add(expected);
            }
        }

        // Find extra products (present in UI but not in Excel)
        for (String actual : actualProducts) {
            if (!expectedProducts.contains(actual)) {
                extraProducts.add(actual);
            }
        }

        // Determine if test passed (no missing or extra products)
        testPassed = missingProducts.isEmpty() && extraProducts.isEmpty();

        // Generate result summary
        generateResultSummary();
    }

    private void generateResultSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== PRODUCT COMPARISON RESULT ===\n");
        summary.append("Entity Type: ").append(entityType).append("\n");
        summary.append("State: ").append(state).append("\n");
        summary.append("Category: ").append(category).append("\n");
        if (county != null && !county.isEmpty()) {
            summary.append("County: ").append(county).append("\n");
        }
        summary.append("\n");

        summary.append("Expected Products (").append(expectedProducts.size()).append("): ");
        summary.append(String.join(", ", expectedProducts)).append("\n");

        summary.append("Actual Products (").append(actualProducts.size()).append("): ");
        summary.append(String.join(", ", actualProducts)).append("\n\n");

        summary.append("✅ MATCHED (").append(matchedProducts.size()).append("): ");
        summary.append(String.join(", ", matchedProducts)).append("\n");

        if (!missingProducts.isEmpty()) {
            summary.append("❌ MISSING (").append(missingProducts.size()).append("): ");
            summary.append(String.join(", ", missingProducts)).append("\n");
        }

        if (!extraProducts.isEmpty()) {
            summary.append("❌ EXTRA (").append(extraProducts.size()).append("): ");
            summary.append(String.join(", ", extraProducts)).append("\n");
        }

        summary.append("\nTest Result: ").append(testPassed ? "PASSED ✅" : "FAILED ❌").append("\n");
        summary.append("================================\n");

        this.resultSummary = summary.toString();
    }

    @Override
    public String toString() {
        return resultSummary != null ? resultSummary : "Comparison not performed yet";
    }
}
