package debug;

import utiles.ExcelUtils;

/**
 * Debug utility to help troubleshoot TC001 issues
 * This will help identify what's in your Excel file and why TC001 might not be found
 */
public class TestCaseDebugger {
    
    public static void main(String[] args) {
        System.out.println("🔍 Excel File Debug Tool");
        System.out.println("=" + "=".repeat(50));
        
        debugExcelFile();
    }
    
    public static void debugExcelFile() {
        ExcelUtils excelUtils = new ExcelUtils();
        String excelFilePath = "src/resources/testdata/FieldInputSheet.xlsx";
        String sheetName = "Sheet1";
        
        try {
            System.out.println("📁 Checking Excel file: " + excelFilePath);
            System.out.println("📄 Sheet name: " + sheetName);
            
            // Check if file exists and get row count
            int totalRows = excelUtils.getRowCount(excelFilePath, sheetName);
            System.out.println("📊 Total rows found: " + totalRows);
            
            if (totalRows == 0) {
                System.err.println("❌ No rows found! Check if:");
                System.err.println("   - File path is correct");
                System.err.println("   - Sheet name is correct (try 'Sheet1', 'Sheet 1', or check actual sheet name)");
                return;
            }
            
            // Column indices based on your description
            int TEST_CASE_ID_COLUMN = 1;  // Column A
            int STEP_DESCRIPTION_COLUMN = 6; // Column F
            int TEST_DATA_COLUMN = 9;     // Column I
            int XPATH_COLUMN = 13;        // Column M
            
            System.out.println("\n🔍 Scanning for test cases...");
            System.out.println("Looking in Column A (index 1) for test case IDs");
            
            boolean foundTC001 = false;
            int tc001Count = 0;
            
            // Check first 20 rows for debugging
            int maxRowsToCheck = Math.min(totalRows, 20);
            
            for (int rowNum = 1; rowNum <= maxRowsToCheck; rowNum++) {
                try {
                    String testCaseId = excelUtils.getCellData(excelFilePath, sheetName, rowNum, TEST_CASE_ID_COLUMN);
                    String description = excelUtils.getCellData(excelFilePath, sheetName, rowNum, STEP_DESCRIPTION_COLUMN);
                    String testData = excelUtils.getCellData(excelFilePath, sheetName, rowNum, TEST_DATA_COLUMN);
                    String xpath = excelUtils.getCellData(excelFilePath, sheetName, rowNum, XPATH_COLUMN);
                    
                    System.out.println("\n📋 Row " + rowNum + ":");
                    System.out.println("   Test Case ID (Col A): '" + testCaseId + "'");
                    System.out.println("   Description (Col F): '" + description + "'");
                    System.out.println("   Test Data (Col I): '" + testData + "'");
                    System.out.println("   XPath (Col M): '" + xpath + "'");
                    
                    if ("TC001".equalsIgnoreCase(testCaseId != null ? testCaseId.trim() : "")) {
                        foundTC001 = true;
                        tc001Count++;
                        
                        if (xpath != null && !xpath.trim().isEmpty()) {
                            System.out.println("   ✅ Valid TC001 step found (has XPath)");
                        } else {
                            System.out.println("   ⚠️ TC001 found but no XPath (will be skipped)");
                        }
                    }
                    
                } catch (Exception e) {
                    System.err.println("❌ Error reading row " + rowNum + ": " + e.getMessage());
                }
            }
            
            System.out.println("\n📊 SUMMARY:");
            System.out.println("Total rows checked: " + maxRowsToCheck);
            System.out.println("TC001 found: " + (foundTC001 ? "YES ✅" : "NO ❌"));
            System.out.println("TC001 occurrences: " + tc001Count);
            
            if (!foundTC001) {
                System.out.println("\n🔧 TROUBLESHOOTING SUGGESTIONS:");
                System.out.println("1. Check if TC001 is in Column A (first column)");
                System.out.println("2. Verify there are no extra spaces around 'TC001'");
                System.out.println("3. Check if sheet name is correct (try different sheet names)");
                System.out.println("4. Verify the Excel file has the correct structure");
                
                // Try to find TC001 in other columns
                System.out.println("\n🔍 Searching for 'TC001' in other columns...");
                searchForTC001InAllColumns(excelUtils, excelFilePath, sheetName, maxRowsToCheck);
            }
            
            // Check available sheet names
            System.out.println("\n📄 Checking available sheet names...");
            try {
                // This is a simple way to check if sheet exists
                excelUtils.getCellData(excelFilePath, "Sheet1", 1, 1);
                System.out.println("✅ 'Sheet1' exists");
            } catch (Exception e) {
                System.out.println("❌ 'Sheet1' not found: " + e.getMessage());
                
                // Try other common sheet names
                String[] commonSheetNames = {"Sheet 1", "TestCases", "Test Cases", "Data", "Main"};
                for (String sheetNameToTry : commonSheetNames) {
                    try {
                        excelUtils.getCellData(excelFilePath, sheetNameToTry, 1, 1);
                        System.out.println("✅ Found sheet: '" + sheetNameToTry + "'");
                    } catch (Exception ex) {
                        System.out.println("❌ Sheet not found: '" + sheetNameToTry + "'");
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error debugging Excel file: " + e.getMessage());
            e.printStackTrace();
            
            System.out.println("\n🔧 POSSIBLE ISSUES:");
            System.out.println("1. Excel file path is incorrect");
            System.out.println("2. Excel file is corrupted or locked");
            System.out.println("3. Sheet name is incorrect");
            System.out.println("4. Excel file format is not supported");
        }
    }
    
    private static void searchForTC001InAllColumns(ExcelUtils excelUtils, String filePath, String sheetName, int maxRows) {
        try {
            for (int rowNum = 1; rowNum <= maxRows; rowNum++) {
                for (int colNum = 1; colNum <= 15; colNum++) { // Check first 15 columns
                    try {
                        String cellValue = excelUtils.getCellData(filePath, sheetName, rowNum, colNum);
                        if (cellValue != null && cellValue.trim().equalsIgnoreCase("TC001")) {
                            System.out.println("🎯 Found 'TC001' at Row " + rowNum + ", Column " + colNum + " (Column " + getColumnLetter(colNum) + ")");
                        }
                    } catch (Exception e) {
                        // Continue searching
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error searching for TC001: " + e.getMessage());
        }
    }
    
    private static String getColumnLetter(int columnNumber) {
        String[] columns = {"", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O"};
        return columnNumber < columns.length ? columns[columnNumber] : "Col" + columnNumber;
    }
}

/**
 * HOW TO USE THIS DEBUGGER:
 * 
 * 1. Run this class to see what's in your Excel file:
 *    java -cp "target/classes:target/dependency/*" debug.TestCaseDebugger
 * 
 * 2. Check the output to see:
 *    - If TC001 is found
 *    - What column it's in
 *    - If XPaths are present
 *    - What the actual data looks like
 * 
 * 3. Based on the output, you can:
 *    - Fix the column indices in TestCaseExecutor
 *    - Fix the sheet name
 *    - Fix the Excel file structure
 */
