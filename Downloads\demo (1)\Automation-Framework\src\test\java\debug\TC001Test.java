package debug;

import drivers.DriverManager;
import utils.TestCaseExecutor;

/**
 * Simple test to debug TC001 execution
 */
public class TC001Test {
    
    public static void main(String[] args) {
        System.out.println("🧪 TC001 Debug Test");
        System.out.println("=" + "=".repeat(30));
        
        try {
            // Initialize WebDriver
            DriverManager.initDriver();
            
            // Create TestCaseExecutor - this will show debug info about Excel file
            TestCaseExecutor executor = new TestCaseExecutor();
            
            // Try to execute TC001 - this will show detailed debug info
            System.out.println("\n🚀 Attempting to execute TC001...");
            boolean result = executor.executeTestCase("TC001");
            
            System.out.println("\n📊 RESULT: " + (result ? "SUCCESS ✅" : "FAILED ❌"));
            
            // Also show available test cases
            System.out.println("\n📋 Available test cases:");
            var availableTestCases = executor.getAvailableTestCases();
            if (availableTestCases.isEmpty()) {
                System.out.println("   ❌ No test cases found!");
            } else {
                for (String testCase : availableTestCases) {
                    System.out.println("   ✅ " + testCase);
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up
            if (DriverManager.getDriver() != null) {
                DriverManager.quitDriver();
            }
        }
    }
}
