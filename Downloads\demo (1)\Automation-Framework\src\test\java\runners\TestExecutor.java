package runners;

import drivers.DriverManager;
import pages.ProductValidator;
import utils.TestCaseExecutor;

import java.util.Scanner;

public class TestExecutor {
    public static void main(String[] args) throws InterruptedException {
        Scanner scanner = new Scanner(System.in);

        System.out.println("🎆 Enhanced Test Executor");
        System.out.println("=" + "=".repeat(30));
        System.out.println("1. Product Validation (LLC/Corp)");
        System.out.println("2. Dynamic Test Case Execution");
        System.out.print("\n👉 Choose option (1 or 2): ");

        String choice = scanner.nextLine().trim();

        DriverManager.initDriver();

        try {
            if ("1".equals(choice)) {
                runProductValidation(scanner);
            } else if ("2".equals(choice)) {
                runDynamicTestCase(scanner);
            } else {
                System.out.println("⚠️ Invalid choice. Running Product Validation by default.");
                runProductValidation(scanner);
            }
        } finally {
            DriverManager.quitDriver();
            scanner.close();
        }
    }

    private static void runProductValidation(Scanner scanner) throws InterruptedException {
        System.out.println("\n📋 Product Validation Mode");
        System.out.print("Enter Entity Type (LLC/Corp/etc.): ");
        String entityType = scanner.nextLine().trim();

        System.out.print("Enter State: ");
        String state = scanner.nextLine().trim();

        // First run login (TC001)
        System.out.println("\n🔐 Running login first...");
        TestCaseExecutor testExecutor = new TestCaseExecutor();
        testExecutor.executeTestCase("TC001");

        Thread.sleep(2000);

        // Then run product validation
        ProductValidator validator = new ProductValidator();
        validator.dynamicOrder(state, entityType);
    }

    private static void runDynamicTestCase(Scanner scanner) {
        System.out.println("\n🎯 Dynamic Test Case Mode");

        TestCaseExecutor testExecutor = new TestCaseExecutor();

        // Show available test cases
        System.out.println("\n📋 Available Test Cases:");
        var availableTestCases = testExecutor.getAvailableTestCases();
        for (int i = 0; i < availableTestCases.size(); i++) {
            System.out.println("  " + (i + 1) + ". " + availableTestCases.get(i));
        }

        System.out.print("\n👉 Enter Test Case ID (e.g., TC001): ");
        String testCaseId = scanner.nextLine().trim().toUpperCase();

        boolean result = testExecutor.executeTestCase(testCaseId);
        System.out.println("\n📊 Result: " + (result ? "PASSED ✅" : "FAILED ❌"));
    }
}
