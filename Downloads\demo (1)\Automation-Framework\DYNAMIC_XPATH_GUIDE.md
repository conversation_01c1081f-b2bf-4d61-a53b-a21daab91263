# 🎯 Dynamic XPath Configuration Guide

This guide explains how to use the **Dynamic XPath System** that allows you to configure UI locators at runtime, making your automation framework adaptable to any application.

## 🚀 What's New - Dynamic XPath Features

### ✅ **Runtime XPath Configuration**
- Set custom XPaths for dropdowns and product lists
- Override default XPaths per test execution
- Support for different applications without code changes

### ✅ **Multiple Fallback Strategies**
- Automatic fallback to alternative locator patterns
- Smart product detection using multiple XPath patterns
- Robust element finding even when UI changes

### ✅ **Custom Product Capture**
- Specify exact XPath for product elements
- Dynamic product list detection
- Support for different product display formats

## 📋 How to Configure XPaths

### Method 1: Set Individual XPaths
```java
ProductFilterPage filterPage = new ProductFilterPage(driver);

// Set custom XPaths for your application
filterPage.setCustomXPath("category", "//select[@id='category-dropdown']");
filterPage.setCustomXPath("entityType", "//div[@data-testid='entity-select']");
filterPage.setCustomXPath("state", "//button[@class='state-button']");
filterPage.setCustomXPath("county", "//input[@name='county']");
```

### Method 2: Set Multiple XPaths at Once
```java
Map<String, String> customXPaths = new HashMap<>();
customXPaths.put("category", "//select[@id='category']");
customXPaths.put("entityType", "//select[@id='entity']");
customXPaths.put("state", "//select[@id='state']");

filterPage.setCustomXPaths(customXPaths);
```

### Method 3: Pass XPaths During Filter Application
```java
filterPage.applyFiltersWithCustomXPaths(
    "Entity Formation",                    // category value
    "LLC",                                // entityType value
    "Alaska",                             // state value
    null,                                 // county value
    "//div[@id='cat-filter']//button",    // category XPath
    "//div[@id='entity-filter']//select", // entityType XPath
    "//div[@id='state-filter']//input",   // state XPath
    null                                  // county XPath
);
```

## 🎯 Usage Examples

### Example 1: Basic Dynamic Configuration
```java
// Initialize with default XPaths
ProductFilterPage filterPage = new ProductFilterPage(driver);

// Update XPaths for your specific application
filterPage.setCustomXPath("category", "//select[@name='category_select']");
filterPage.setCustomXPath("state", "//div[@class='state-dropdown']//button");

// Apply filters - will use your custom XPaths
filterPage.applyFilters("Entity Formation", "LLC", "Alaska", null);

// Capture products with custom XPath
List<String> products = filterPage.captureProductsWithCustomXPath(
    "//div[@class='product-grid']//div[@class='product-card']//h3"
);
```

### Example 2: Complete Product Validation with Custom XPaths
```java
// Create validator
ProductValidator validator = new ProductValidator();

// Get the filter page and configure XPaths
ProductFilterPage filterPage = new ProductFilterPage(driver);
filterPage.setCustomXPath("category", "YOUR_CATEGORY_XPATH");
filterPage.setCustomXPath("entityType", "YOUR_ENTITY_XPATH");
filterPage.setCustomXPath("state", "YOUR_STATE_XPATH");

// Run validation - will use your custom XPaths
ProductComparisonResult result = validator.validateProductsForCategory(
    "LLC", "Alaska", "Entity Formation", null
);
```

### Example 3: Reading XPaths from Excel
```java
// Read XPaths from Excel configuration
ExcelUtils excelUtils = new ExcelUtils();
String categoryXPath = excelUtils.getCellData("config.xlsx", "XPaths", 1, 2);
String stateXPath = excelUtils.getCellData("config.xlsx", "XPaths", 2, 2);
String productXPath = excelUtils.getCellData("config.xlsx", "XPaths", 3, 2);

// Configure the filter page
ProductFilterPage filterPage = new ProductFilterPage(driver);
filterPage.setCustomXPath("category", categoryXPath);
filterPage.setCustomXPath("state", stateXPath);

// Apply filters and capture products
filterPage.applyFilters("Entity Formation", "LLC", "Alaska", null);
List<String> products = filterPage.captureProductsWithCustomXPath(productXPath);
```

## 📊 Excel Configuration Structure

You can store XPaths in Excel for easy management:

### XPath Configuration Sheet:
| Element Type | XPath | Description |
|--------------|-------|-------------|
| category | //select[@id='category-select'] | Category dropdown |
| entityType | //div[@data-testid='entity']//button | Entity type selector |
| state | //input[@name='state_input'] | State input field |
| products | //div[@class='results']//div[@class='product-item']//span | Product names |

### Test Case with XPaths:
| Test Case | Category XPath | Entity XPath | State XPath | Product XPath |
|-----------|----------------|--------------|-------------|---------------|
| TC001 | //select[@id='cat'] | //select[@id='entity'] | //select[@id='state'] | //div[@class='products']//span |
| TC002 | //div[@data-test='cat'] | //div[@data-test='entity'] | //div[@data-test='state'] | //ul[@class='product-list']//li |

## 🔧 Common XPath Patterns

### Dropdown Selectors:
```xpath
# Standard select dropdown
//select[@id='dropdown-id']
//select[@name='dropdown-name']

# Custom dropdown (div/button based)
//div[@class='dropdown-container']//button
//div[@data-testid='dropdown']//div[@class='trigger']

# PrimeNG dropdown (common in Angular apps)
//p-dropdown[@id='dropdown-id']//div[contains(@class,'p-dropdown')]
```

### Product List Patterns:
```xpath
# Grid layout
//div[@class='product-grid']//div[@class='product-card']//h3
//div[@class='products']//div[@class='item']//span[@class='title']

# List layout  
//ul[@class='product-list']//li//span[@class='product-name']
//div[@class='results']//div[@class='product-item']//div[@class='name']

# Table layout
//table[@class='products']//tr//td[@class='product-name']
//div[@class='table']//div[@class='row']//div[@class='product-cell']
```

### State/Location Selectors:
```xpath
# Standard select
//select[@name='state']//option

# Button-based selector
//div[@class='state-selector']//button[contains(text(),'Alaska')]

# Input with autocomplete
//input[@placeholder='Select State']
//div[@class='autocomplete']//input[@name='state']
```

## 🎮 Integration with Test Cases

### Method 1: Store XPaths in Test Data
```java
public class TestCaseWithXPaths {
    public void executeTestWithCustomXPaths(String testCaseId) {
        // Read test case data including XPaths
        TestData testData = readTestCaseData(testCaseId);
        
        // Configure filter page with test-specific XPaths
        ProductFilterPage filterPage = new ProductFilterPage(driver);
        filterPage.setCustomXPath("category", testData.getCategoryXPath());
        filterPage.setCustomXPath("state", testData.getStateXPath());
        
        // Execute test
        filterPage.applyFilters(testData.getCategory(), testData.getEntityType(), 
                               testData.getState(), testData.getCounty());
    }
}
```

### Method 2: Environment-Specific XPaths
```java
public class EnvironmentSpecificXPaths {
    public void configureForEnvironment(String environment) {
        ProductFilterPage filterPage = new ProductFilterPage(driver);
        
        if ("staging".equals(environment)) {
            filterPage.setCustomXPath("category", "//div[@data-test='category-staging']");
            filterPage.setCustomXPath("state", "//div[@data-test='state-staging']");
        } else if ("production".equals(environment)) {
            filterPage.setCustomXPath("category", "//select[@id='category-prod']");
            filterPage.setCustomXPath("state", "//select[@id='state-prod']");
        }
    }
}
```

## 🔍 Troubleshooting XPath Issues

### 1. **Element Not Found**
```
❌ XPath for Category dropdown is not configured
```
**Solution**: Set the XPath before using:
```java
filterPage.setCustomXPath("category", "YOUR_CATEGORY_XPATH");
```

### 2. **Option Selection Failed**
```
❌ Could not find option 'LLC' in Entity Type dropdown
```
**Solution**: The system tries multiple option patterns automatically. Check if:
- The dropdown opens correctly
- The option text matches exactly
- The option is visible and clickable

### 3. **No Products Found**
```
⚠️ No products found with standard locators, trying alternative methods...
```
**Solution**: Provide a custom product XPath:
```java
List<String> products = filterPage.captureProductsWithCustomXPath(
    "//div[@class='your-product-container']//span[@class='product-name']"
);
```

### 4. **XPath Validation**
Test your XPaths in browser console:
```javascript
// Test in browser console
$x("//select[@id='category-select']")  // Should return elements
$x("//div[@class='products']//span")   // Should return product elements
```

## 🚀 Advanced Features

### 1. **Dynamic Pattern Matching**
The system automatically tries multiple patterns:
```java
// Container patterns (tried in order)
"//div[contains(@class,'product-list')]"
"//div[contains(@class,'products')]" 
"//div[contains(@class,'right')]"
"//div[contains(@class,'results')]"

// Item patterns (tried for each container)
".//div[contains(@class,'product-item')]"
".//li[contains(@class,'product')]"
".//span[contains(@class,'product')]"
".//div[contains(@class,'title')]"
```

### 2. **Smart Option Selection**
Multiple option selection strategies:
```java
"//li[contains(text(),'" + optionText + "')]"
"//li[contains(@data-label,'" + optionText + "')]"
"//option[contains(text(),'" + optionText + "')]"
"//div[contains(text(),'" + optionText + "')]"
"//span[contains(text(),'" + optionText + "')]"
```

### 3. **Fallback Mechanisms**
- Default XPaths as fallback
- Multiple locator strategies
- Alternative product capture methods
- Graceful error handling

## 📝 Best Practices

### 1. **XPath Design**
- Use stable attributes (id, data-testid, name)
- Avoid position-based XPaths (avoid [1], [2])
- Use contains() for partial matches
- Keep XPaths as short as possible

### 2. **Configuration Management**
- Store XPaths in external files (Excel, JSON, properties)
- Use environment-specific configurations
- Version control your XPath configurations
- Document XPath changes

### 3. **Testing Strategy**
- Test XPaths in multiple browsers
- Validate XPaths after UI changes
- Use the DynamicXPathExample.java for testing
- Monitor XPath stability over time

---

**🎉 With dynamic XPath configuration, your automation framework can now adapt to any application by simply updating the locators without changing the core test logic!**
